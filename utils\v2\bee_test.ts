/*
 * @Author: zgr126 <EMAIL>
 * @Date: 2024-11-24 15:58:56
 * @LastEditors: zgr126 <EMAIL>
 * @LastEditTime: 2024-11-24 16:02:36
 * @FilePath: \hhh\utils\v2\bee_test.ts
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
 */
import ccxt from "ccxt";
import { <PERSON> } from "./bee";
import { oneDayTime } from "@utils/hhh2/type";
import { Hive } from "./hive";

let hive = new Hive(new ccxt.pro.binance())
let bee = hive.bee('btc')

let ohlcvs = await bee.fetchOHLCV(new Date().getTime() - oneDayTime, 1000*3600*6)
console.log(ohlcvs)