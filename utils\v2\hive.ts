/*
 * @Author: zgr126 <EMAIL>
 * @Date: 2024-11-24 15:51:36
 * @LastEditors: zgr126 <EMAIL>
 * @LastEditTime: 2024-11-24 17:07:04
 * @FilePath: \hhh\utils\v2\hive.ts
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
import { Exchange, OHLCV } from "ccxt";
import { SymbolName } from "./functions/ohlcv";
import { Bee } from "./bee";
import { Squad } from "./squad";

export class Hive {
  beeLst: Bee[] = [];
  exchange: Exchange;
  constructor(exchange: Exchange) {
    this.exchange = exchange;
  }
  bee(value: Bee): Bee;
  bee(value: string): Bee;
  bee(value: string | Bee): Bee {
    let bee: Bee;
    if (typeof value === "string") {
      bee = new Bee(value, this);
    } else {
      bee = value;
    }

    this.beeLst.push(bee);
    return bee;
  }
  removeBee(bee: <PERSON>) {
    let index = this.beeLst.findIndex((e) => {
      return e.id == bee.id;
    });
    if (index >= 0) {
      this.beeLst.splice(index, 1);
    }
  }

  squad(coinName: string): Squad {
    return new Squad(coinName, this);
  }
}
