/*
 * @Author: zgr126 <EMAIL>
 * @Date: 2025-07-09
 * @Description: 指标计算服务 - 提供各种技术指标的计算功能
 * 
 * Copyright (c) 2025 by zgr126, All Rights Reserved. 
 */

import { OHLCV } from 'ccxt'
import { LineData, HistogramData } from 'lightweight-charts'

export interface IndicatorConfig {
  id: string
  name: string
  type: 'sma' | 'ema' | 'rsi' | 'macd' | 'bollinger' | 'stoch' | 'cci' | 'custom'
  overlay: boolean
  parameters: Record<string, any>
  color?: string
  lineWidth?: number
}

export interface IndicatorResult {
  id: string
  data: LineData[] | HistogramData[]
  config: IndicatorConfig
}

export class IndicatorService {
  /**
   * 计算简单移动平均线 (SMA)
   */
  static calculateSMA(ohlcv: OHLCV[], period: number = 20): LineData[] {
    const closes = ohlcv.map(([, , , , close]) => close)
    const result: LineData[] = []

    for (let i = period - 1; i < closes.length; i++) {
      const sum = closes.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)
      const average = sum / period
      
      result.push({
        time: Math.floor(ohlcv[i][0] / 1000) as any,
        value: average
      })
    }

    return result
  }

  /**
   * 计算指数移动平均线 (EMA)
   */
  static calculateEMA(ohlcv: OHLCV[], period: number = 20): LineData[] {
    const closes = ohlcv.map(([, , , , close]) => close)
    const result: LineData[] = []
    const multiplier = 2 / (period + 1)

    // 第一个EMA值使用SMA
    let ema = closes.slice(0, period).reduce((a, b) => a + b, 0) / period
    result.push({
      time: Math.floor(ohlcv[period - 1][0] / 1000) as any,
      value: ema
    })

    // 计算后续EMA值
    for (let i = period; i < closes.length; i++) {
      ema = (closes[i] - ema) * multiplier + ema
      result.push({
        time: Math.floor(ohlcv[i][0] / 1000) as any,
        value: ema
      })
    }

    return result
  }

  /**
   * 计算相对强弱指数 (RSI)
   */
  static calculateRSI(ohlcv: OHLCV[], period: number = 14): LineData[] {
    const closes = ohlcv.map(([, , , , close]) => close)
    const result: LineData[] = []
    
    if (closes.length < period + 1) return result

    // 计算价格变化
    const changes: number[] = []
    for (let i = 1; i < closes.length; i++) {
      changes.push(closes[i] - closes[i - 1])
    }

    // 计算初始平均涨跌幅
    let avgGain = 0
    let avgLoss = 0
    
    for (let i = 0; i < period; i++) {
      if (changes[i] > 0) {
        avgGain += changes[i]
      } else {
        avgLoss += Math.abs(changes[i])
      }
    }
    
    avgGain /= period
    avgLoss /= period

    // 计算RSI
    for (let i = period; i < changes.length; i++) {
      const change = changes[i]
      
      if (change > 0) {
        avgGain = (avgGain * (period - 1) + change) / period
        avgLoss = (avgLoss * (period - 1)) / period
      } else {
        avgGain = (avgGain * (period - 1)) / period
        avgLoss = (avgLoss * (period - 1) + Math.abs(change)) / period
      }

      const rs = avgGain / avgLoss
      const rsi = 100 - (100 / (1 + rs))

      result.push({
        time: Math.floor(ohlcv[i + 1][0] / 1000) as any,
        value: rsi
      })
    }

    return result
  }

  /**
   * 计算MACD
   */
  static calculateMACD(ohlcv: OHLCV[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9): {
    macd: LineData[]
    signal: LineData[]
    histogram: HistogramData[]
  } {
    const fastEMA = this.calculateEMA(ohlcv, fastPeriod)
    const slowEMA = this.calculateEMA(ohlcv, slowPeriod)
    
    const macd: LineData[] = []
    const signal: LineData[] = []
    const histogram: HistogramData[] = []

    // 计算MACD线
    const startIndex = Math.max(0, slowPeriod - fastPeriod)
    for (let i = startIndex; i < Math.min(fastEMA.length, slowEMA.length); i++) {
      const macdValue = fastEMA[i + (slowPeriod - fastPeriod)]?.value - slowEMA[i]?.value
      if (macdValue !== undefined) {
        macd.push({
          time: slowEMA[i].time,
          value: macdValue
        })
      }
    }

    // 计算信号线 (MACD的EMA)
    if (macd.length >= signalPeriod) {
      const multiplier = 2 / (signalPeriod + 1)
      let signalEMA = macd.slice(0, signalPeriod).reduce((sum, item) => sum + item.value, 0) / signalPeriod
      
      signal.push({
        time: macd[signalPeriod - 1].time,
        value: signalEMA
      })

      for (let i = signalPeriod; i < macd.length; i++) {
        signalEMA = (macd[i].value - signalEMA) * multiplier + signalEMA
        signal.push({
          time: macd[i].time,
          value: signalEMA
        })
      }
    }

    // 计算柱状图 (MACD - Signal)
    for (let i = 0; i < Math.min(macd.length, signal.length); i++) {
      const signalIndex = i - (macd.length - signal.length)
      if (signalIndex >= 0) {
        const histValue = macd[i].value - signal[signalIndex].value
        histogram.push({
          time: macd[i].time,
          value: histValue,
          color: histValue >= 0 ? '#26a69a' : '#ef5350'
        })
      }
    }

    return { macd, signal, histogram }
  }

  /**
   * 计算布林带
   */
  static calculateBollingerBands(ohlcv: OHLCV[], period: number = 20, stdDev: number = 2): {
    upper: LineData[]
    middle: LineData[]
    lower: LineData[]
  } {
    const closes = ohlcv.map(([, , , , close]) => close)
    const upper: LineData[] = []
    const middle: LineData[] = []
    const lower: LineData[] = []

    for (let i = period - 1; i < closes.length; i++) {
      const slice = closes.slice(i - period + 1, i + 1)
      const sma = slice.reduce((a, b) => a + b, 0) / period
      
      // 计算标准差
      const variance = slice.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period
      const standardDeviation = Math.sqrt(variance)
      
      const time = Math.floor(ohlcv[i][0] / 1000) as any
      
      upper.push({ time, value: sma + (standardDeviation * stdDev) })
      middle.push({ time, value: sma })
      lower.push({ time, value: sma - (standardDeviation * stdDev) })
    }

    return { upper, middle, lower }
  }

  /**
   * 计算随机指标 (Stochastic)
   */
  static calculateStochastic(ohlcv: OHLCV[], kPeriod: number = 14, dPeriod: number = 3): {
    k: LineData[]
    d: LineData[]
  } {
    const k: LineData[] = []
    const d: LineData[] = []

    // 计算%K
    for (let i = kPeriod - 1; i < ohlcv.length; i++) {
      const slice = ohlcv.slice(i - kPeriod + 1, i + 1)
      const highest = Math.max(...slice.map(([, , high]) => high))
      const lowest = Math.min(...slice.map(([, , , low]) => low))
      const close = ohlcv[i][4]
      
      const kValue = ((close - lowest) / (highest - lowest)) * 100
      
      k.push({
        time: Math.floor(ohlcv[i][0] / 1000) as any,
        value: kValue
      })
    }

    // 计算%D (K的移动平均)
    for (let i = dPeriod - 1; i < k.length; i++) {
      const slice = k.slice(i - dPeriod + 1, i + 1)
      const dValue = slice.reduce((sum, item) => sum + item.value, 0) / dPeriod
      
      d.push({
        time: k[i].time,
        value: dValue
      })
    }

    return { k, d }
  }

  /**
   * 计算商品通道指数 (CCI)
   */
  static calculateCCI(ohlcv: OHLCV[], period: number = 20): LineData[] {
    const result: LineData[] = []
    const constant = 0.015

    for (let i = period - 1; i < ohlcv.length; i++) {
      const slice = ohlcv.slice(i - period + 1, i + 1)
      
      // 计算典型价格
      const typicalPrices = slice.map(([, , high, low, close]) => (high + low + close) / 3)
      const sma = typicalPrices.reduce((a, b) => a + b, 0) / period
      
      // 计算平均偏差
      const meanDeviation = typicalPrices.reduce((sum, tp) => sum + Math.abs(tp - sma), 0) / period
      
      const currentTypicalPrice = (ohlcv[i][2] + ohlcv[i][3] + ohlcv[i][4]) / 3
      const cci = (currentTypicalPrice - sma) / (constant * meanDeviation)
      
      result.push({
        time: Math.floor(ohlcv[i][0] / 1000) as any,
        value: cci
      })
    }

    return result
  }

  /**
   * 根据配置计算指标
   */
  static calculateIndicator(ohlcv: OHLCV[], config: IndicatorConfig): IndicatorResult {
    let data: LineData[] | HistogramData[] = []

    switch (config.type) {
      case 'sma':
        data = this.calculateSMA(ohlcv, config.parameters.period || 20)
        break
      
      case 'ema':
        data = this.calculateEMA(ohlcv, config.parameters.period || 20)
        break
      
      case 'rsi':
        data = this.calculateRSI(ohlcv, config.parameters.period || 14)
        break
      
      case 'macd':
        const macdResult = this.calculateMACD(
          ohlcv,
          config.parameters.fastPeriod || 12,
          config.parameters.slowPeriod || 26,
          config.parameters.signalPeriod || 9
        )
        // 返回MACD线，其他线需要单独处理
        data = macdResult.macd
        break
      
      case 'bollinger':
        const bbResult = this.calculateBollingerBands(
          ohlcv,
          config.parameters.period || 20,
          config.parameters.stdDev || 2
        )
        // 返回中线，其他线需要单独处理
        data = bbResult.middle
        break
      
      case 'stoch':
        const stochResult = this.calculateStochastic(
          ohlcv,
          config.parameters.kPeriod || 14,
          config.parameters.dPeriod || 3
        )
        // 返回%K线，%D线需要单独处理
        data = stochResult.k
        break
      
      case 'cci':
        data = this.calculateCCI(ohlcv, config.parameters.period || 20)
        break
      
      default:
        console.warn(`不支持的指标类型: ${config.type}`)
    }

    return {
      id: config.id,
      data,
      config
    }
  }

  /**
   * 批量计算多个指标
   */
  static calculateMultipleIndicators(ohlcv: OHLCV[], configs: IndicatorConfig[]): IndicatorResult[] {
    return configs.map(config => this.calculateIndicator(ohlcv, config))
  }
}
