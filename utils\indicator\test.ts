import { OHLCV } from "ccxt";
import * as indicators from "./index.ts";
import { Asset, movingAverageConvergenceDivergence } from "./index.ts";
import { CalcResult } from "../v2view/type.ts";
let config: indicators.MACDConfig = {};
export function ohlcvtoAsset(ohlcvLst: OHLCV[]): Asset {
  let asset: Asset = {
    closings: [],
    dates: [],
    highs: [],
    lows: [],
    openings: [],
    volumes: [],
  };
  // dates: Date[];
  // openings: number[];
  // closings: number[];
  // highs: number[];
  // lows: number[];
  // volumes: number[];
  ohlcvLst.map((ohlcv) => {
    asset.dates.push(new Date(ohlcv[0] as number));
    asset.openings.push(ohlcv[1] as number);
    asset.highs.push(ohlcv[2] as number);
    asset.lows.push(ohlcv[3] as number);
    asset.closings.push(ohlcv[4] as number);
    asset.volumes.push(ohlcv[5] as number);
  });
  return asset;
}

export function calcmacd(
  asset: Asset,
  options?: number[]
): indicators.MACDResult {
  let params: indicators.MACDConfig = {};
  if (options) {
    params = {
      fast: options[0],
      slow: options[1],
      signal: options[2],
    };
  }
  console.log("jjj");
  const macd = movingAverageConvergenceDivergence(asset.closings, params);
  return macd;
}
