/*
 * @Author: zgr126 <EMAIL>
 * @Date: 2025-07-09
 * @Description: 市场数据服务 - 管理多交易所数据获取、缓存和实时更新
 * 
 * Copyright (c) 2025 by zgr126, All Rights Reserved. 
 */

import ccxt, { Exchange, OHLCV, Ticker, Market } from 'ccxt'
import { db } from '@utils/v2/tool'

export interface MarketDataConfig {
  exchange: string
  symbol: string
  timeframe: string
  limit?: number
}

export interface CachedOHLCV {
  id: string
  exchange: string
  symbol: string
  timeframe: string
  data: OHLCV[]
  lastUpdate: number
  nextUpdate: number
}

export interface ExchangeInfo {
  id: string
  name: string
  instance: Exchange
  markets: Record<string, Market>
  connected: boolean
}

export class MarketDataService {
  private exchanges: Map<string, ExchangeInfo> = new Map()
  private cache: Map<string, CachedOHLCV> = new Map()
  private updateIntervals: Map<string, NodeJS.Timeout> = new Map()
  private subscribers: Map<string, Set<(data: OHLCV[]) => void>> = new Map()

  constructor() {
    this.initializeExchanges()
  }

  /**
   * 初始化支持的交易所
   */
  private async initializeExchanges() {
    const exchangeConfigs = [
      { id: 'binance', class: ccxt.binance, name: 'Binance' },
      { id: 'okx', class: ccxt.okx, name: 'OKX' },
      { id: 'bybit', class: ccxt.bybit, name: 'Bybit' },
      { id: 'binanceusdm', class: ccxt.binanceusdm, name: 'Binance USDM' }
    ]

    for (const config of exchangeConfigs) {
      try {
        const instance = new config.class({
          sandbox: false,
          enableRateLimit: true,
          options: {
            defaultType: 'future' // 默认使用合约
          }
        })

        const exchangeInfo: ExchangeInfo = {
          id: config.id,
          name: config.name,
          instance,
          markets: {},
          connected: false
        }

        this.exchanges.set(config.id, exchangeInfo)
        
        // 异步加载市场数据
        this.loadMarkets(config.id)
      } catch (error) {
        console.error(`初始化交易所 ${config.id} 失败:`, error)
      }
    }
  }

  /**
   * 加载交易所市场数据
   */
  private async loadMarkets(exchangeId: string) {
    const exchangeInfo = this.exchanges.get(exchangeId)
    if (!exchangeInfo) return

    try {
      const markets = await exchangeInfo.instance.loadMarkets()
      exchangeInfo.markets = markets
      exchangeInfo.connected = true
      console.log(`${exchangeInfo.name} 市场数据加载完成，共 ${Object.keys(markets).length} 个交易对`)
    } catch (error) {
      console.error(`加载 ${exchangeInfo.name} 市场数据失败:`, error)
      exchangeInfo.connected = false
    }
  }

  /**
   * 获取支持的交易所列表
   */
  getExchanges(): ExchangeInfo[] {
    return Array.from(this.exchanges.values())
  }

  /**
   * 获取交易所的交易对列表
   */
  getSymbols(exchangeId: string, filter?: { active?: boolean; type?: string }): string[] {
    const exchangeInfo = this.exchanges.get(exchangeId)
    if (!exchangeInfo || !exchangeInfo.connected) return []

    let symbols = Object.keys(exchangeInfo.markets)

    if (filter) {
      symbols = symbols.filter(symbol => {
        const market = exchangeInfo.markets[symbol]
        if (filter.active !== undefined && market.active !== filter.active) return false
        if (filter.type && market.type !== filter.type) return false
        return true
      })
    }

    return symbols.sort()
  }

  /**
   * 搜索交易对
   */
  searchSymbols(exchangeId: string, query: string, limit = 20): string[] {
    const symbols = this.getSymbols(exchangeId, { active: true })
    const lowerQuery = query.toLowerCase()
    
    return symbols
      .filter(symbol => symbol.toLowerCase().includes(lowerQuery))
      .slice(0, limit)
  }

  /**
   * 获取OHLCV数据
   */
  async fetchOHLCV(config: MarketDataConfig): Promise<OHLCV[]> {
    const { exchange, symbol, timeframe, limit = 500 } = config
    const cacheKey = `${exchange}_${symbol}_${timeframe}`

    // 检查缓存
    const cached = this.cache.get(cacheKey)
    const now = Date.now()
    
    if (cached && now < cached.nextUpdate) {
      return cached.data
    }

    // 从交易所获取数据
    const exchangeInfo = this.exchanges.get(exchange)
    if (!exchangeInfo || !exchangeInfo.connected) {
      throw new Error(`交易所 ${exchange} 未连接`)
    }

    try {
      const ohlcv = await exchangeInfo.instance.fetchOHLCV(symbol, timeframe, undefined, limit)
      
      // 更新缓存
      const timeframeMs = this.getTimeframeMs(timeframe)
      const cachedData: CachedOHLCV = {
        id: cacheKey,
        exchange,
        symbol,
        timeframe,
        data: ohlcv,
        lastUpdate: now,
        nextUpdate: now + Math.min(timeframeMs / 4, 60000) // 最多1分钟更新一次
      }

      this.cache.set(cacheKey, cachedData)

      // 保存到数据库
      this.saveToDatabase(cachedData)

      // 通知订阅者
      this.notifySubscribers(cacheKey, ohlcv)

      return ohlcv
    } catch (error) {
      console.error(`获取 ${exchange} ${symbol} ${timeframe} 数据失败:`, error)
      
      // 尝试从数据库加载
      const dbData = await this.loadFromDatabase(cacheKey)
      if (dbData) {
        return dbData.data
      }
      
      throw error
    }
  }

  /**
   * 获取实时价格
   */
  async fetchTicker(exchange: string, symbol: string): Promise<Ticker> {
    const exchangeInfo = this.exchanges.get(exchange)
    if (!exchangeInfo || !exchangeInfo.connected) {
      throw new Error(`交易所 ${exchange} 未连接`)
    }

    return await exchangeInfo.instance.fetchTicker(symbol)
  }

  /**
   * 订阅数据更新
   */
  subscribe(config: MarketDataConfig, callback: (data: OHLCV[]) => void): () => void {
    const cacheKey = `${config.exchange}_${config.symbol}_${config.timeframe}`
    
    if (!this.subscribers.has(cacheKey)) {
      this.subscribers.set(cacheKey, new Set())
    }
    
    this.subscribers.get(cacheKey)!.add(callback)

    // 启动自动更新
    this.startAutoUpdate(config)

    // 返回取消订阅函数
    return () => {
      const subscribers = this.subscribers.get(cacheKey)
      if (subscribers) {
        subscribers.delete(callback)
        if (subscribers.size === 0) {
          this.stopAutoUpdate(cacheKey)
        }
      }
    }
  }

  /**
   * 启动自动更新
   */
  private startAutoUpdate(config: MarketDataConfig) {
    const cacheKey = `${config.exchange}_${config.symbol}_${config.timeframe}`
    
    if (this.updateIntervals.has(cacheKey)) return

    const timeframeMs = this.getTimeframeMs(config.timeframe)
    const updateInterval = Math.min(timeframeMs / 4, 60000) // 最多1分钟更新一次

    const interval = setInterval(async () => {
      try {
        await this.fetchOHLCV(config)
      } catch (error) {
        console.error(`自动更新 ${cacheKey} 失败:`, error)
      }
    }, updateInterval)

    this.updateIntervals.set(cacheKey, interval)
  }

  /**
   * 停止自动更新
   */
  private stopAutoUpdate(cacheKey: string) {
    const interval = this.updateIntervals.get(cacheKey)
    if (interval) {
      clearInterval(interval)
      this.updateIntervals.delete(cacheKey)
    }
  }

  /**
   * 通知订阅者
   */
  private notifySubscribers(cacheKey: string, data: OHLCV[]) {
    const subscribers = this.subscribers.get(cacheKey)
    if (subscribers) {
      subscribers.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('通知订阅者失败:', error)
        }
      })
    }
  }

  /**
   * 保存到数据库
   */
  private async saveToDatabase(data: CachedOHLCV) {
    try {
      await db.bee.put({
        id: data.id,
        exchange: data.exchange,
        symbol: data.symbol,
        startTime: data.data[0]?.[0] || 0,
        interval: this.getTimeframeMs(data.timeframe),
        ohlcv: data.data
      })
    } catch (error) {
      console.error('保存到数据库失败:', error)
    }
  }

  /**
   * 从数据库加载
   */
  private async loadFromDatabase(cacheKey: string): Promise<CachedOHLCV | null> {
    try {
      const record = await db.bee.get(cacheKey)
      if (record) {
        return {
          id: record.id,
          exchange: record.exchange,
          symbol: record.symbol,
          timeframe: this.getMsToTimeframe(record.interval),
          data: record.ohlcv,
          lastUpdate: 0,
          nextUpdate: 0
        }
      }
    } catch (error) {
      console.error('从数据库加载失败:', error)
    }
    return null
  }

  /**
   * 时间周期转毫秒
   */
  private getTimeframeMs(timeframe: string): number {
    const timeframes: Record<string, number> = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000,
      '1w': 7 * 24 * 60 * 60 * 1000
    }
    return timeframes[timeframe] || 60 * 60 * 1000
  }

  /**
   * 毫秒转时间周期
   */
  private getMsToTimeframe(ms: number): string {
    const timeframes: Record<number, string> = {
      [60 * 1000]: '1m',
      [5 * 60 * 1000]: '5m',
      [15 * 60 * 1000]: '15m',
      [30 * 60 * 1000]: '30m',
      [60 * 60 * 1000]: '1h',
      [4 * 60 * 60 * 1000]: '4h',
      [24 * 60 * 60 * 1000]: '1d',
      [7 * 24 * 60 * 60 * 1000]: '1w'
    }
    return timeframes[ms] || '1h'
  }

  /**
   * 清理资源
   */
  destroy() {
    // 清理所有定时器
    this.updateIntervals.forEach(interval => clearInterval(interval))
    this.updateIntervals.clear()
    
    // 清理订阅者
    this.subscribers.clear()
    
    // 清理缓存
    this.cache.clear()
  }
}

// 单例实例
export const marketDataService = new MarketDataService()
