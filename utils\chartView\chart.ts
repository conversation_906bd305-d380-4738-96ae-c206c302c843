import { createChart } from "lightweight-charts";
import { VertLine } from "./verticalline";

// lightChart 配置
const chartOptions = {
  // autoSize: true,
  width: window.innerWidth,
  height: 200,
  layout: {
    textColor: "black",
    background: {
      type: "solid",
      color: "white",
    },
    attributionLogo: false,
  },
  timeScale: {
    visible: false,
    timeVisible: true,
    secondsVisible: true,
  },
  localization: {},
  rightPriceScale: {
    minimumWidth: 150,
    entireTextOnly: true,
    mode: 2,
  },
  crosshair: {
    mode: 0,
  },
};
let timeFormatter = (businessDayOrTimestamp) => {
  const t = businessDayOrTimestamp * 1000;
  let d: Date = new Date();
  d.format("yyyy-MM-dd hh:mm:ss");
  const str = new Date(t).format("yy/M/d hh:mm:ss");
  // console.log("time:", new Date().getTime(), "ltime:", t);
  return str;
};
let chartOptionsIn = structuredClone(chartOptions);
chartOptions.localization.timeFormatter = timeFormatter;
chartOptionsIn.localization.timeFormatter = timeFormatter;
chartOptionsIn.rightPriceScale.mode = 0;
////

export class ChartManager {
  boxLst: ChartBox[] = [];
  config = {};
  RangeLock = null;
  nowChat = null;
  tempMousePositionEvent = null; // 缓存鼠标位置事件,用于计算缩放中心点
  constructor() {}
  addChart(interval: number, ohlcv) {
    const box = new ChartBox(this, interval, "source", "", ohlcv);
    this.boxLst.push(box);
    return box;
  }
  removeChart(...intervals) {
    for (i in intervals) {
      this.boxLst.delete((e) => {
        return e.interval == i;
      });
    }
  }
  addSubChart(interval, calcName, calcs) {
    let find = this.boxLst.find((e) => {
      return e.name === calcName && e.interval === interval;
    });
    if (!find) {
      // let insertIndex = 0;
      // let findIndex = this.boxLst.findIndex((e) => {
      //   return e.type === "source" && e.interval === interval;
      // });
      // insertIndex = findIndex == -1 ? this.boxLst.length - 1 : findIndex;
      const box = new ChartBox(this, interval, "indicator", calcName, calcs);
      // this.boxLst.splice(insertIndex + 1, 0, box);
      this.boxLst.push(box);
      return box;
    }
  }

  removeSubChart(calcName, interval) {
    if (interval) {
      let index = this.boxLst.findIndex((e) => {
        return e.interval == interval && e.name === calcName;
      });
      this.boxLst.splice(index, 1);
    } else {
      this.boxLst.delete((e) => {
        return e.name === calcName;
      });
    }
  }

  subData(e) {
    if (e.type === "change") {
      const data = this.boxLst.filter((i) => {
        return e.time === i.time;
      });
      data.map((item) => {
        item.Series.setData(e.data);
      });
    } else {
      console.log(e);
    }
  }
  // 子box事件监听
  subEvent(chart, eventName, param) {
    // console.log(eventName);
    const needGetNew = (e) => {
      const from2 = chart.chart.timeScale().getVisibleLogicalRange();
      if (from2.from < 0) {
        chart.ohlcv.getHistory(500, chart.Series.datas[0].time * 1000, -1);
      } else {
        chart.ohlcv.getHistory(500, chart.Series.datas.last().time * 1000, 1);
      }
    };
    switch (eventName) {
      case "CrosshairMove":
        this.tempMousePositionEvent = param;
        // 鼠标移动事件
        // needGetNew()
        this.#filterTImeWithoutSelf(chart, (chartBox) => {
          const targetChart = chartBox.chart;
          const targetSeries = chartBox.Series.series[0];
          const series = chart.Series.series[0];
          const dataPoint = param.seriesData.get(series);
          if (dataPoint) {
            targetChart.setCrosshairPosition(
              dataPoint?.value || dataPoint?.close,
              dataPoint?.time,
              targetSeries
            );
          } else {
            targetChart.clearCrosshairPosition();
          }

          // chart.clearCrosshairPosition();
        });
        break;
      case "VisibleLogicalRangeChange":
        // 缩放平移事件
        // 防止循环触发
        if (this.RangeLock) return;
        this.RangeLock = true;
        setTimeout((e) => {
          this.RangeLock = false;
        }, 0);
        // needGetNew()
        // 设置所有缩放一致
        this.boxLst.map((e) => {
          if (!(chart.interval == e.interval && chart.name == e.name)) {
            let itemScale = e.chart.timeScale().getVisibleLogicalRange();
            if (!itemScale) {
              return;
            }
            if (
              Math.abs(
                itemScale.to - itemScale.from - (param.to - param.from)
              ) < 2
            ) {
              return;
            } else {
              if (this.tempMousePositionEvent?.point) {
                // time
                let logical = e.chart
                  .timeScale()
                  .coordinateToLogical(this.tempMousePositionEvent.point.x);

                let range = e.chart.timeScale().getVisibleLogicalRange();
                let ratio = (param.to - param.from) / (range.to - range.from);
                let result = {};
                result.from = logical - (logical - range.from) * ratio;
                result.to = logical + (range.to - logical) * ratio;
                e.chart.timeScale().setVisibleLogicalRange(result);
                // zoom event
                // let scale = itemScale;
                // let t = (param.to - param.from) / 2;
                // let scaleT = scale.from + (scale.to - scale.from) / 2;
                // param.from = scaleT - t;
                // param.to = scaleT + t;
                // e.chart.timeScale().setVisibleLogicalRange(param);
              }
            }
          }
        });
        this.#filterTImeWithoutSelf(chart, (e) => {
          const range = chart.chart.timeScale().getVisibleLogicalRange();
          e.chart.timeScale().setVisibleLogicalRange(range);
        });
        // if (this.nowChat){
        //   return
        // }
        // this.boxLst.filter(e=>{
        //   if(e.time !== chart.time){
        //     this.nowChat = chart
        //     setTimeout(e=>{
        //       this.nowChat = null
        //     },100)
        //     let scale = e.chart.timeScale().getVisibleLogicalRange()
        //     let t = (param.to - param.from)/2
        //     let scaleT = scale.from + (scale.to - scale.from)/2
        //     param.from = scaleT-t
        //     param.to = scaleT+t
        //     chart.chart.timeScale().setVisibleLogicalRange(param);
        //   }
        // })
        break;
      case "click":
        console.log(param);
        if (!param.time) return;
        let clicktime = chart.chart.timeScale().getVisibleRange();
        let point = param.point;
        let nowScale = chart.chart.timeScale();
        nowScale.scrollToPosition(nowScale.scrollPosition());
        chart.Series.setVertLine(chart, param);
        const nowlogicalId = nowScale.coordinateToLogical(
          nowScale.timeToCoordinate(param.time)
        );
        const clickLogictoFrom =
          nowlogicalId - nowScale.getVisibleLogicalRange().from; // 点击到的蜡烛柱与当前图最左侧的差值（单位柱子数量）
        this.boxLst.map((e) => {
          // e.chart.timeScale().setVisibleLogicalRange(s);
          // chart.Series.setMarke(param.time)

          // 移动坐标
          if (!(chart.interval == e.interval && chart.name == e.name)) {
            e.Series.setVertLine(e, param);
            let time = e.Series.datas.findIndex((e) => {
              return String(Number(e.time)) > param.time;
            });
            if (time == -1) {
              time = e.Series.datas.length;
            }
            const timeScale = e.chart.timeScale();
            const logical = timeScale.getVisibleLogicalRange();
            const itemWidth =
              e.chart.chartElement().getElementsByTagName("canvas")[0].width /
              (logical.to - logical.from);
            const toScroll =
              time -
              e.Series.datas.length +
              logical.to -
              logical.from -
              (clickLogictoFrom * itemWidth) / itemWidth;

            // const currentPos = timeScale.scrollToPosition(toScroll,false);
            const currentPos = timeScale.scrollToPosition(toScroll, false);
          }

          // e.Series.series[0].attachPrimitive(vertLine);
        });
    }
  }
  filterTime(chart, func) {
    let boxLst = this.boxLst.filter((e) => {
      return e.interval === chart.interval;
    });
    boxLst.map((e) => {
      func(e);
    });
  }
  #filterTImeWithoutSelf(chart, func) {
    let boxLst = this.boxLst.filter((e) => {
      return e.interval === chart.interval;
    });
    boxLst = boxLst.filter((e) => {
      return e.name != chart.name;
    });
    boxLst.map((e) => {
      func(e);
    });
  }
}
export class ChartBox {
  id: string;
  manage: ChartManager;
  name: string; // macd
  interval = ""; // 1m
  chart = null;
  Series = null;
  type = ""; // source | indicator
  state = 0;
  legents = [];
  ohlcv = [];
  constructor(manage, interval, type, name, ohlcv) {
    let a = "";
    this.id = String.random(10);
    this.ohlcv = ohlcv;
    this.manage = manage;
    this.interval = interval;
    this.type = type;
    this.name = name;
    this.domName = `${this.interval}_${this.name}`;
  }
  // 再次设定ohlcv
  setOhlcv(ohlcv) {
    this.ohlcv = ohlcv;
  }
  mark(time, type) {
    this.Series.mark(time, type);
  }
  // 需等待生成完毕才能获取dom
  makeChart() {
    let option = this.type === "source" ? chartOptions : chartOptionsIn;
    option = JSON.parse(JSON.stringify(option));
    // option.width = document.getElementById(this.domName + "_box").offsetWidth;
    // console.log(option.width);
    if (this.type === "source") {
      option.height = 200;
      option.timeScale.visible = true;
    } else {
      option.height = 100;
    }
    option.localization.timeFormatter = timeFormatter;
    this.chart = createChart(document.getElementById(this.id), option);
    this.chart.subscribeClick((e) => {
      this.manage.subEvent(this, "click", e);
    });
    this.chart.subscribeCrosshairMove((e) => {
      this.manage.subEvent(this, "CrosshairMove", e);
      if (!e.seriesData) return;
      this.legents = [];
      let series = this.Series.series;
      this.Series.options.map((item, index) => {
        return {
          name: item.name,
          value: e.seriesData.get(series[index])?.value,
          color: item.color,
        };
      });
    });

    let series = new Series(this.ohlcv, this.chart, this.type, this.name);
    this.Series = series;
    this.chart.timeScale().subscribeVisibleLogicalRangeChange((e) => {
      this.manage.subEvent(this, "VisibleLogicalRangeChange", e);
    });
  }
}

export class Series {
  type = "";
  series = [];
  datas = [];
  options = [];
  ohlcv = null;
  chart = null;
  name = "";
  vertLine = null;
  constructor(ohlcv, chart, type, name) {
    this.name = name;
    this.ohlcv = ohlcv;
    this.type = type;
    this.chart = chart;
    this.initSeries(chart, type, name);
    // this.initSeries(chart, "", "MACD");
  }
  mark(time, type) {
    let lst = this.series[0].markers();
    let m = {};
    m.time = time;
    switch (type) {
      case "buy":
        m.position = "belowBar";
        m.color = "#2196F3";
        m.shape = "arrowUp";
        m.text = "买入";
        break;
      case "sell":
        m.position = "aboveBar";
        m.color = "#e91e63";
        m.shape = "arrowDown";
        m.text = "卖出";
    }
    lst.push(m);
    this.series[0].setMarkers(lst);
  }
  initSeries(chart, type, name) {
    if (type === "source") {
      this.options = [{ name: "source", type: "candle" }];
      this.setSeries(chart, this.options);
      this.setData(this.ohlcv);
    } else {
      switch (name) {
        case "MACD":
          this.options = [
            { name: "m", type: "line", map: "macdLine", color: "#333" },
            { name: "s", type: "line", map: "signalLine", color: "#efb32c" },
          ];
          break;
        case "KDJ":
          this.options = [
            { name: "k", type: "line", map: "k", color: "#333" },
            { name: "d", type: "line", map: "d", color: "#efb32c" },
            { name: "j", type: "line", map: "j", color: "#efb32c" },
          ];
          break;
        case "SAR":
          this.options = [
            { name: "m", type: "line", map: "psar", color: "#333" },
          ];
          break;
      }
      this.setSeries(chart, this.options);
      this.setData();
    }
  }
  setVertLine(chartBox, params) {
    if (this.vertLine) {
      this.series[0].detachPrimitive(this.vertLine);
    }
    this.vertLine = new VertLine(
      chartBox,
      chartBox.chart,
      chartBox.Series.series[0],
      params.time,
      {
        showLabel: false,
        labelText: "Hello",
      }
    );

    this.series[0].attachPrimitive(this.vertLine);
    this.vertLine.updateAllViews();
  }
  setMarke(time) {
    const volumeSeries = this.chart.addHistogramSeries({
      color: "#26a69a",
      priceFormat: {
        type: "volume",
      },
      priceRange: {
        minValue: 0,
        maxValue: 100,
      },
      priceScaleId: "", // set as an overlay by setting a blank priceScaleId
      // set the positioning of the volume series
      scaleMargins: {
        top: -1, // highest point of the series will be 70% away from the top
        bottom: -10000,
      },
    });
    volumeSeries.setData([{ time: time, value: 9999999, color: "#26a69aaa" }]);
    this.series.push(volumeSeries);
  }
  setData(data) {
    if (this.type === "source") {
      this.datas = this.formatOHLCVData(data);
      this.series[0].setData(this.datas);
    } else {
      let data = this.ohlcv.data.find((e) => {
        return e.name === this.name.toLowerCase();
      });

      // for (let i = 0; i < this.series.length; i++) {
      //   const op = this.options[i];
      //   const datas = data.data[op.map];
      //   this.series[i].setData(this.formatTimeDatas(datas));
      // }
      this.series.map((item, index) => {
        const op = this.options[index];
        const datas = data.data[op.map];
        this.datas = this.formatTimeDatas(datas, this.ohlcv.timeLst);
        item.setData(this.datas);
      });
    }
  }
  formatTimeDatas(data, times) {
    let _data = [];
    if (!data) return;
    data.map((e, index) => {
      _data.push({
        time: times[index] / 1000,
        value: e,
      });
    });
    return _data;
  }
  formatOHLCVData(datas) {
    let _data = [];
    datas.map((e) => {
      _data.push({
        close: Number(e[4]),
        high: Number(e[2]),
        low: Number(e[3]),
        open: Number(e[1]),
        time: Number(e[0] / 1000),
        volume: Number(e[5]),
        value: Number(e[4]),
      });
    });
    return _data;
  }
  setSeries(chart, list) {
    list.map((item) => {
      let series = null;
      switch (item.type) {
        case "candle":
          series = chart.addCandlestickSeries({
            upColor: "#26a69a",
            downColor: "#ef5350",
            borderVisible: false,
            wickUpColor: "#26a69a",
            wickDownColor: "#ef5350",
          });

          break;
        case "line":
          series = chart.addLineSeries({
            lineWidth: 1,
            color: item.color,
          });
          break;
      }
      this.series.push(series);
    });
  }
}
