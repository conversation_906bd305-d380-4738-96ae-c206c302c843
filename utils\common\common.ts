Array.prototype.last = function <T>():T | null {
  let l = this.length;
  if (l > 0) {
    return this[this.length - 1];
  } else {
    return null;
  }
};

Array.prototype.delete = function (func):void {
  for (let i = this.length - 1; i >= 0; i--) {
    if (func(this[i])) {
      this.splice(i, 1);
    }
  }
};

Array.prototype.random = function <T>():T {
  let id = Math.floor(Math.random() * this.length);
  return this[id];
};

Date.prototype.format = function (format: string) {
  let date = this;
  const o: {[key:string|number]: string|number} = {
    "M+": date.getMonth() + 1, // 月份
    "d+": date.getDate(), // 日
    "h+": date.getHours(), //% 12 === 0 ? 12 : date.getHours() % 12, // 小时
    "H+": date.getHours(), // 小时
    "m+": date.getMinutes(), // 分
    "s+": date.getSeconds(), // 秒
    "q+": Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
    a: date.getHours() < 12 ? "上午" : "下午", // 上午/下午
    A: date.getHours() < 12 ? "AM" : "PM", // AM/PM
  };
  if (/(y+)/.test(format)) {
    format = format.replace(
      RegExp.$1,
      (date.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }
  for (let k in o) {
    if (new RegExp("(" + k + ")").test(format)) {
      let v = o[k] as string
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? v : ("00" + v).substr(("" + v).length)
      );
    }
  }
  return format;
};
// 随机数生成
String.random = (length) => {
  let result = "";
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }

  return result;
};
