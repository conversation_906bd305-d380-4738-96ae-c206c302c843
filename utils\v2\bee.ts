// deno-lint-ignore-file prefer-const
/*
 * @Author: zgr126 <EMAIL>
 * @Date: 2024-11-10 21:59:22
 * @LastEditors: zgr126 <EMAIL>
 * @LastEditTime: 2024-11-24 16:23:38
 * @FilePath: \hhh\utils\v2\bee.ts
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */

import { CallBackFunc, OHLCVLst } from "./type.ts";
import { getOHLCVs, SymbolName } from "./functions/ohlcv.ts";
import { Hive } from "./hive.ts";
import { ohlcvtoAsset } from "@utils/indicator/test.ts";
import { Asset } from "@utils/indicator/index.ts";
import { v7 as uuid } from "uuid";
import { buildOHLCV } from "./functions/ohlcv";
import { OHLCV } from "ccxt";

export class Bee {
  id: string;
  startTime: number = 0;
  endTime: number = 0;
  interval: number = 0;

  private _ohlcvLst: OHLCVLst = [];
  asset: Asset | undefined;
  name: string;
  symbol: string;
  hive: Hive;
  constructor(coinName: string, hive: Hive) {
    this.id = uuid();
    this.name = coinName;
    this.symbol = SymbolName(coinName);
    this.hive = hive;
    return this;
  }

  get ohlcvLst() {
    return this._ohlcvLst;
  }
  set ohlcvLst(value: OHLCVLst) {
    this._ohlcvLst = value;
    this.startTime = this._ohlcvLst[0][0] as number;
    this.endTime = this._ohlcvLst[this.ohlcvLst.length - 1][0] as number;
    this.asset = ohlcvtoAsset(value);
  }
  // 设置OHLCV
  setOHLCV(ohlcvLst: OHLCVLst, interval: number) {
    this.ohlcvLst = ohlcvLst;
    this.interval = interval;
  }
  // 获取ohlcv
  async fetchOHLCV(
    startTime: number,
    intervalTime: number,
    callBack?: CallBackFunc
  ): Promise<OHLCVLst> {
    if (this.ohlcvLst.length > 0) {
      startTime = this.ohlcvLst[this.ohlcvLst.length - 1][0] as number;
    }
    let ohlcvs = await getOHLCVs(
      this.symbol,
      this.hive.exchange,
      startTime,
      intervalTime,
      callBack
    );
    this.ohlcvLst = ohlcvs;
    this.interval = 1;
    return ohlcvs;
  }
  buildBee(interval: number): Bee {
    let ohlcvs = buildOHLCV(this.ohlcvLst, interval);
    let bee = new Bee(this.symbol, this.hive);
    bee.setOHLCV(ohlcvs, interval);
    this.hive.bee(bee);
    return bee;
  }
}

// export const testOHLCV = async (): Promise<OHLCVLst> => {
//   let hive = new Hive(new ccxt.pro.binance())
//   const man = new Bee("btc", exchange);
//   console.log(man);
//   const r = await man.fetchOHLCV(1730380000000, 1000 * 3600 * 6);
//   console.log(r);
//   let oLst = man.buildOHLCV(60);
//   console.log(oLst);
//   return oLst;
// };
