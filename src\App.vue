<!--
 * @Author: zgr126 <EMAIL>
 * @Date: 2024-11-12 23:20:37
 * @LastEditors: zgr126 <EMAIL>
 * @LastEditTime: 2025-06-23 00:14:15
 * @FilePath: \hhh\src\App.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<script setup lang="ts">
import { useRouter } from 'vue-router';
const router = useRouter();
</script>

<template>
  <button @click="router.push('/exchangeLst')">列表</button>
  <RouterView />

</template>

<style scoped lang="less"></style>
