# Momentum Strategies

Momentum strategies generate signals based on a momentum indicator.

- [Momentum Strategies](#momentum-strategies)
  - [Awesome Oscillator Strategy](#awesome-oscillator-strategy)
  - [Ichimoku Cloud Strategy](#ichimoku-cloud-strategy)
  - [RSI 2 Strategy](#rsi-2-strategy)
  - [Stochastic Oscillator Strategy](#stochastic-oscillator-strategy)
  - [Williams R Strategy](#williams-r-strategy)
  - [Disclaimer](#disclaimer)
  - [License](#license)

**NOTE:** All configuration objects for all strategies are optional. If no configuration object is passed, the default configuration will be used. Likewise, you may also partially pass a configuration object, and the default values will be used for the missing properties.

## Awesome Oscillator Strategy

The [awesomeOscillatorStrategy](./awesomeOscillatorStrategy.ts) uses the _ao_ values that are generated by the [Awesome Oscillator](../../indicator/momentum/README.md#awesome-oscillator-ao) indicator function to provide a SELL action when the _ao_ is below zero, and a BUY action when _ao_ is above zero.

```TypeScript
import { aoStrategy } from 'indicatorts';

const defaultConfig = { fast: 5, slow: 34 };
const actions = aoStrategy(asset, defaultConfig);

// Alternatively:
// const actions = awesomeOscillatorStrategy(asset, defaultConfig);
```

## Ichimoku Cloud Strategy

The [ichimokuCloudStrategy](./ichimokuCloudStrategy.ts) uses the _ao_ values that are generated by the [Ichimoku Cloud](../../indicator/momentum/README.md#ichimoku-cloud) indicator function to provide a _BUY_ action when the _leadingSpanA_ is greather than _leadingSpanB_, and a _SELL_ action when the _leadingSpanA_ is less than _leadingSpanB_, and a _HOLD_ action when _leadingSpanA_ is equal to _leadingSpanB_.

```TypeScript
import { ichimokuCloudStrategy } from 'indicatorts';

const defaultConfig = { short: 9, medium: 26, long: 52, close: 26 };
const actions = ichimokuCloudStrategy(asset, defaultConfig);
```

## RSI 2 Strategy

The [rsi2Strategy](./rsi2Strategy.ts) uses the _rsi_ values that are generated by the [RSI 2](../../indicator/momentum/README.md#relative-strength-index-rsi) indicator function to provide a _BUY_ action when 2-period RSI moves below 10, and a _SELL_ action when the 2-period RSI moved above 90, and a _HOLD_ action otherwise.

```TypeScript
import { rsi2Strategy } from 'indicatorts';

const actions = rsi2Strategy(asset);
```

## Stochastic Oscillator Strategy

The [stochasticOscillatorStrategy](./stochasticOscillatorStrategy.ts) uses the _ao_ values that are generated by the [Stochastic Oscillator](../../indicator/momentum/README.md#stochastic-oscillator-stoch) indicator function to provide a _BUY_ action when _k_ and _d_ are less than 20, a _SELL_ action when the _k_ and _d_ are greather than 80, a _HOLD_ action otherwise.

```TypeScript
import { stochStrategy } from 'indicatorts';

const defaultConfig = { kPeriod: 14, dPeriod: 3 };
const actions = stochStrategy(asset, defaultConfig);

// Alternatively:
// const actions = stochasticOscillatorStrategy(asset, defaultConfig);
```

## Williams R Strategy

The [williamsRStrategy](./williamsRStrategy.ts) uses the _wr_ values that are generated by the [Williams R](../../indicator/momentum/README.md#williams-r-willr) indicator function to provide a SELL action when the _wr_ is below -20, and a BUY action when _wr_ is above -80.

```TypeScript
import { willRStrategy } from 'indicatorts';

const defaultConfig = { period: 14 };
const actions = willRStrategy(asset, defaultConfig);

// Alternatively:
// const actions = williamsRStrategy(asset, defaultConfig);
```

## Disclaimer

The information provided on this project is strictly for informational purposes and is not to be construed as advice or solicitation to buy or sell any security.

## License

Copyright (c) 2022 Onur Cinar. All Rights Reserved.

The source code is provided under MIT License.
