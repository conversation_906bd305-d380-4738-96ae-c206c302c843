// Copyright (c) 2022 Onur Cinar. All Rights Reserved.
// https://github.com/cinar/indicatorts

import { ema } from '../../indicator/trend/exponentialMovingAverage';
import {
  NVIConfig,
  NVIDefaultConfig,
  nvi,
} from '../../indicator/volume/negativeVolumeIndex';
import { Action } from '../action';
import { Asset } from '../asset';

/**
 * The negative volume index strategy uses the nvi values that are generated by
 * the Negative Volume Index (NVI) indicator function to provide a BUY action
 * when nvi is less than its 255-period EMA, and a SELL action when it is
 * greather than its 255-period EMA, otherwise a HOLD action.
 *
 * @param asset asset object.
 * @param config configuration.
 * @returns strategy actions.
 */
export function nviStrategy(asset: Asset, config: NVIConfig = {}): Action[] {
  const strategyConfig = { ...NVIDefaultConfig, ...config };
  const result = nvi(asset.closings, asset.volumes, strategyConfig);

  const nviEma = ema(result, { period: strategyConfig.period });

  const actions = new Array<Action>(result.length);

  for (let i = 0; i < actions.length; i++) {
    if (result[i] < nviEma[i]) {
      actions[i] = Action.BUY;
    } else if (result[i] > nviEma[i]) {
      actions[i] = Action.SELL;
    } else {
      actions[i] = Action.HOLD;
    }
  }

  return actions;
}

// Export full name
export { nviStrategy as negativeVolumeIndexStrategy };
