/*
 * @Author: zgr126 <EMAIL>
 * @Date: 2024-11-24 15:51:36
 * @LastEditors: zgr126 <EMAIL>
 * @LastEditTime: 2024-11-24 17:12:49
 * @FilePath: \hhh\utils\v2\tool.ts
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
import ccxt, { Bool, OHLCV } from "ccxt";
import { Bee } from "./bee";
import Dexie, { EntityTable } from "dexie";
import { Hive } from "./hive";

export interface beeDB {
  id: string;
  // exchange name
  exchange: string;
  symbol: string;
  startTime: number;
  interval: number;
  ohlcv: OHLCV[];
}

// Database declaration (move this to its own module also)
export const db = new Dexie("beeDB") as <PERSON><PERSON> & {
  bee: EntityTable<beeDB, "id">;
};

db.version(1).stores({
  bee: "id,exchange,symbol,startTime,interval",
});

// 保存
export async function saveBeetoDB(beeLst: Bee[]): Promise<Boolean> {
  let asyncLst: any[] = [];
  beeLst.map(async (bee) => {
    let promise = db.bee
      .get({
        exchange: bee.hive.exchange.name,
        symbol: bee.symbol,
        startTime: bee.startTime,
      })
      .then((item) => {
        let result = {
          exchange: bee.hive.exchange.name as string,
          id: bee.id,
          symbol: bee.symbol,
          startTime: bee.startTime,
          interval: bee.interval,
          ohlcv: bee.ohlcvLst,
        };
        if (!item) {
          return db.bee.add(result);
        }
      });
    asyncLst.push(promise);
  });
  return Promise.all(asyncLst).then(() => true);
}

// 获取全部

export async function getAllBee(hive?: Hive): Promise<Bee[]> {
  if (!hive) {
    hive = new Hive(new ccxt.pro.binance());
  }
  return db.bee.toArray().then((beeLst) => {
    let lst: Bee[] = [];
    beeLst.map((_bee) => {
      let bee = new Bee(_bee.symbol, hive);
      bee.setOHLCV(_bee.ohlcv, _bee.interval);
      lst.push(bee);
    });
    return lst;
  });
}

// 获取单个
