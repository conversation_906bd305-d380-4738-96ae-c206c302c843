// Copyright (c) 2022 Onur Cinar. All Rights Reserved.
// https://github.com/cinar/indicatorts

import { deepStrictEqual } from 'assert';
import { roundDigitsAll } from '../../helper/numArray';
import { ppo } from './percentagePriceOscillator';

describe('Percent Price Oscillator (PPO)', () => {
  const prices = [
    3674.84, 3666.77, 3789.99, 3735.48, 3749.63, 3900.86, 4017.82, 4115.77,
    4160.68, 4121.43, 4108.54, 4176.82, 4101.23, 4132.15, 4158.24, 4057.84,
    3978.73, 3941.48, 3973.75, 3901.36, 3900.79, 3923.68, 4088.85, 4008.01,
    4023.89, 3930.08, 3935.18, 4001.05, 3991.24, 4123.34, 4146.87, 4300.17,
    4175.48, 4155.38, 4131.93, 4287.5, 4183.96, 4175.2, 4296.12, 4271.78,
    4393.66, 4459.45, 4462.21, 4391.69, 4392.59, 4446.59, 4397.45, 4412.53,
    4488.28, 4500.21, 4481.15, 4525.12, 4582.64, 4545.86, 4530.41, 4602.45,
    4631.6, 4575.52, 4543.06, 4520.16, 4456.24, 4511.61, 4461.18, 4463.12,
    4411.67, 4357.86, 4262.45, 4173.11, 4204.31, 4259.52, 4277.88, 4170.7,
    4201.09, 4328.87, 4363.49, 4386.54, 4306.26, 4373.94, 4384.65, 4288.7,
    4225.5, 4304.76, 4348.87, 4380.26, 4475.01, 4471.07, 4401.67, 4418.64,
    4504.08, 4587.18,
  ];

  it('should be able to compute with a config', () => {
    const expectedPPO = [
      0, -0.03, 0.43, 0.48, 0.55, 1.14, 1.89, 2.61, 3.09, 3.11, 2.94, 2.95,
      2.57, 2.34, 2.19, 1.66, 0.99, 0.41, 0.15, -0.28, -0.53, -0.58, 0, 0.07,
      0.17, -0.12, -0.27, -0.11, -0.05, 0.46, 0.84, 1.56, 1.5, 1.34, 1.1, 1.44,
      1.24, 1.03, 1.27, 1.29, 1.65, 2.01, 2.16, 1.92, 1.71, 1.68, 1.43, 1.27,
      1.36, 1.4, 1.31, 1.33, 1.48, 1.39, 1.23, 1.3, 1.39, 1.21, 0.94, 0.67,
      0.28, 0.2, -0.02, -0.14, -0.38, -0.68, -1.15, -1.69, -1.86, -1.7, -1.48,
      -1.64, -1.57, -1.03, -0.55, -0.16, -0.17, 0.05, 0.21, -0.01, -0.35, -0.29,
      -0.09, 0.14, 0.58, 0.81, 0.7, 0.65, 0.87, 1.24,
    ];

    const expectedSignal = [
      0, -0.01, 0.1, 0.2, 0.28, 0.5, 0.84, 1.29, 1.74, 2.08, 2.29, 2.46, 2.48,
      2.45, 2.38, 2.2, 1.9, 1.53, 1.18, 0.82, 0.48, 0.21, 0.16, 0.14, 0.15,
      0.08, -0.01, -0.03, -0.04, 0.09, 0.28, 0.6, 0.82, 0.95, 0.99, 1.1, 1.14,
      1.11, 1.15, 1.18, 1.3, 1.48, 1.65, 1.72, 1.71, 1.71, 1.64, 1.54, 1.5,
      1.48, 1.43, 1.41, 1.43, 1.42, 1.37, 1.35, 1.36, 1.32, 1.23, 1.09, 0.89,
      0.71, 0.53, 0.36, 0.18, -0.04, -0.31, -0.66, -0.96, -1.14, -1.23, -1.33,
      -1.39, -1.3, -1.11, -0.87, -0.7, -0.51, -0.33, -0.25, -0.28, -0.28, -0.23,
      -0.14, 0.04, 0.23, 0.35, 0.42, 0.54, 0.71,
    ];

    const expectedHistogram = [
      0, -0.02, 0.33, 0.28, 0.26, 0.64, 1.04, 1.32, 1.35, 1.03, 0.64, 0.49,
      0.08, -0.11, -0.19, -0.54, -0.91, -1.12, -1.03, -1.09, -1.01, -0.8, -0.16,
      -0.07, 0.02, -0.2, -0.26, -0.08, -0.01, 0.38, 0.56, 0.97, 0.68, 0.38,
      0.11, 0.34, 0.1, -0.08, 0.12, 0.1, 0.35, 0.54, 0.51, 0.21, -0.01, -0.03,
      -0.21, -0.28, -0.14, -0.07, -0.13, -0.07, 0.05, -0.03, -0.14, -0.05, 0.03,
      -0.12, -0.28, -0.42, -0.61, -0.52, -0.55, -0.51, -0.56, -0.64, -0.83,
      -1.03, -0.9, -0.56, -0.25, -0.31, -0.18, 0.27, 0.56, 0.72, 0.52, 0.56,
      0.54, 0.24, -0.08, -0.01, 0.14, 0.28, 0.54, 0.58, 0.35, 0.23, 0.34, 0.52,
    ];

    const actual = ppo(prices, {
      fast: 6,
      slow: 13,
      signal: 7,
    });

    deepStrictEqual(roundDigitsAll(2, actual.ppoResult), expectedPPO);
    deepStrictEqual(roundDigitsAll(2, actual.signal), expectedSignal);
    deepStrictEqual(roundDigitsAll(2, actual.histogram), expectedHistogram);
  });

  it('should be able to compute without a config', () => {
    const expectedPPO = [
      0, -0.02, 0.24, 0.31, 0.4, 0.79, 1.33, 1.93, 2.47, 2.77, 2.95, 3.19, 3.18,
      3.2, 3.22, 3.01, 2.65, 2.26, 2, 1.63, 1.31, 1.1, 1.25, 1.19, 1.16, 0.94,
      0.77, 0.75, 0.71, 0.94, 1.15, 1.6, 1.69, 1.7, 1.64, 1.88, 1.84, 1.77,
      1.93, 1.98, 2.23, 2.52, 2.72, 2.71, 2.68, 2.71, 2.62, 2.55, 2.6, 2.62,
      2.58, 2.6, 2.68, 2.65, 2.56, 2.6, 2.64, 2.55, 2.39, 2.19, 1.9, 1.75, 1.52,
      1.33, 1.07, 0.77, 0.34, -0.16, -0.49, -0.65, -0.73, -0.99, -1.12, -0.98,
      -0.79, -0.59, -0.58, -0.43, -0.3, -0.36, -0.53, -0.5, -0.4, -0.25, 0.04,
      0.25, 0.3, 0.36, 0.55, 0.85,
    ];

    const expectedSignal = [
      0, -0, 0.04, 0.1, 0.16, 0.28, 0.49, 0.78, 1.12, 1.45, 1.75, 2.04, 2.27,
      2.45, 2.61, 2.69, 2.68, 2.6, 2.48, 2.31, 2.11, 1.91, 1.77, 1.66, 1.56,
      1.44, 1.3, 1.19, 1.1, 1.06, 1.08, 1.19, 1.29, 1.37, 1.42, 1.51, 1.58,
      1.62, 1.68, 1.74, 1.84, 1.98, 2.13, 2.24, 2.33, 2.41, 2.45, 2.47, 2.49,
      2.52, 2.53, 2.55, 2.57, 2.59, 2.58, 2.59, 2.6, 2.59, 2.55, 2.48, 2.36,
      2.24, 2.1, 1.94, 1.77, 1.57, 1.32, 1.03, 0.72, 0.45, 0.21, -0.03, -0.25,
      -0.39, -0.47, -0.5, -0.51, -0.5, -0.46, -0.44, -0.46, -0.47, -0.45, -0.41,
      -0.32, -0.21, -0.11, -0.01, 0.1, 0.25,
    ];

    const expectedHistogram = [
      0, -0.01, 0.19, 0.22, 0.24, 0.5, 0.83, 1.15, 1.35, 1.32, 1.2, 1.15, 0.91,
      0.75, 0.62, 0.32, -0.03, -0.33, -0.48, -0.68, -0.79, -0.81, -0.52, -0.47,
      -0.4, -0.49, -0.54, -0.44, -0.38, -0.13, 0.07, 0.42, 0.4, 0.33, 0.22,
      0.36, 0.26, 0.15, 0.25, 0.24, 0.39, 0.55, 0.6, 0.47, 0.35, 0.31, 0.17,
      0.08, 0.1, 0.1, 0.05, 0.05, 0.11, 0.06, -0.02, 0.01, 0.05, -0.04, -0.16,
      -0.28, -0.46, -0.49, -0.57, -0.61, -0.69, -0.8, -0.98, -1.18, -1.22, -1.1,
      -0.95, -0.96, -0.88, -0.58, -0.32, -0.09, -0.06, 0.06, 0.16, 0.07, -0.07,
      -0.04, 0.05, 0.16, 0.36, 0.46, 0.4, 0.37, 0.45, 0.6,
    ];

    const actual = ppo(prices);
    deepStrictEqual(roundDigitsAll(2, actual.ppoResult), expectedPPO);
    deepStrictEqual(roundDigitsAll(2, actual.signal), expectedSignal);
    deepStrictEqual(roundDigitsAll(2, actual.histogram), expectedHistogram);
  });
});
