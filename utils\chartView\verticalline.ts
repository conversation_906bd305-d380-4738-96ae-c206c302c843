import { CanvasRenderingTarget2D } from 'fancy-canvas';
import zoom from '../common/zoom'
import {
	type Coordinate,
	type IChartApi,
	type ISeriesApi,
	type ISeriesPrimitive,
	type ISeriesPrimitiveAxisView,
	type ISeriesPrimitivePaneRenderer,
	type ISeriesPrimitivePaneView,
	type SeriesOptionsMap,
	type SeriesType,
	type Time,
} from 'lightweight-charts';
// import { positionsLine } from '../../helpers/dimensions/positions';

class VertLinePaneRenderer implements ISeriesPrimitivePaneRenderer {
	_x: Coordinate | null = null;
	_options: VertLineOptions;
	_vline: VertLine;
	constructor(vline: VertLine, x: Coordinate | null, options: VertLineOptions) {
		this._x = x;
		this._options = options;
		this._vline = vline;
		// vline.updateAllViews()
	}
	draw(target: CanvasRenderingTarget2D) {
		// const ctx = target._context
		// // const position = positionsLine(
		// // 	this._x,
		// // 	scope.horizontalPixelRatio,
		// // 	this._options.width
		// // );
		// ctx.fillStyle = this._options.color;
		// ctx.fillRect(
		// 	this._x,//position.position,
		// 	0,
		// 	1,//position.length,
		// 	target._bitmapSize.height+50
		// );
		target.useBitmapCoordinateSpace(scope => {
			if (this._x === null) return;
			const ctx = scope.context;
			// const position = positionsLine(
			// 	this._x,
			// 	scope.horizontalPixelRatio,
			// 	this._options.width
			// );
			ctx.fillStyle = this._options.color;
			ctx.fillRect(
				this._x,//position.position,
				0,
				1,//position.length,
				scope.bitmapSize.height+50
			);
		});
	}
}

class VertLinePaneView implements ISeriesPrimitivePaneView {
	_source: VertLine;
	_x: Coordinate | null = null;
	_options: VertLineOptions;

	constructor(source: VertLine, options: VertLineOptions) {
		this._source = source;
		this._options = options;
	}
	update(vbv) {
		
		let time = this._source.__chart.Series.datas.findIndex(e=>{
				return String(Number(e.time))>this._source._time
		})
		let t = 0
		if (time == -1){
			t = this._source.__chart.Series.datas.last().time
		}else{
			t = this._source.__chart.Series.datas[time-1].time
		}
		
		t = Number(t)
		const timeScale = this._source._chart.timeScale();
		this._x = timeScale.timeToCoordinate(t);
		this._x = this._x * zoom
		this.renderer()
	}
	renderer() {
		return new VertLinePaneRenderer(this._source, this._x, this._options);
	}
}

class VertLineTimeAxisView implements ISeriesPrimitiveAxisView {
	_source: VertLine;
	_x: Coordinate | null = null;
	_options: VertLineOptions;

	constructor(source: VertLine, options: VertLineOptions) {
		this._source = source;
		this._options = options;
	}
	update() {
		const timeScale = this._source._chart.timeScale();
		this._x = timeScale.timeToCoordinate(this._source._time);
	}
	visible() {
		return this._options.showLabel;
	}
	tickVisible() {
		return this._options.showLabel;
	}
	coordinate() {
		return this._x ?? 0;
	}
	text() {
		return this._options.labelText;
	}
	textColor() {
		return this._options.labelTextColor;
	}
	backColor() {
		return this._options.labelBackgroundColor;
	}
}

export interface VertLineOptions {
	color: string;
	labelText: string;
	width: number;
	labelBackgroundColor: string;
	labelTextColor: string;
	showLabel: boolean;
}

const defaultOptions: VertLineOptions = {
	color: 'green',
	labelText: '',
	width: 3,
	labelBackgroundColor: 'green',
	labelTextColor: 'white',
	showLabel: false,
};

export class VertLine implements ISeriesPrimitive<Time> {
    __chart: any;
	_chart: IChartApi;
	_series: ISeriesApi<keyof SeriesOptionsMap>;
	_time: Time;
	_paneViews: VertLinePaneView[];
	_timeAxisViews: VertLineTimeAxisView[];

	constructor(
        _chart: any,
		chart: IChartApi,
		series: ISeriesApi<SeriesType>,
		time: Time,
		options?: Partial<VertLineOptions>
	) {
		const vertLineOptions: VertLineOptions = {
			...defaultOptions,
			...options,
		};
        this.__chart = _chart
		this._chart = chart;
		this._series = series;
		this._time = time;
		this._paneViews = [new VertLinePaneView(this, vertLineOptions)];
		this._timeAxisViews = [new VertLineTimeAxisView(this, vertLineOptions)];
	}
	updateAllViews() {
		this._paneViews.forEach(pw => pw.update());
		this._timeAxisViews.forEach(tw => tw.update());
	}
	timeAxisViews() {		return this._timeAxisViews;
	}
	paneViews() {
		return this._paneViews;
	}
}