# Volatility Strategies

Volatility strategies generate signals based on a volatility indicator.

- [Volatility Strategies](#volatility-strategies)
  - [Acceleration Bands Strategy](#acceleration-bands-strategy)
  - [Bollinger Bands Strategy](#bollinger-bands-strategy)
  - [Projection Oscillator Strategy](#projection-oscillator-strategy)
  - [Disclaimer](#disclaimer)
  - [License](#license)

**NOTE:** All configuration objects for all strategies are optional. If no configuration object is passed, the default configuration will be used. Likewise, you may also partially pass a configuration object, and the default values will be used for the missing properties.

## Acceleration Bands Strategy

The [accelerationBandsStrategy](./accelerationBandsStrategy.ts) uses the _upperBand_, and _lowerBand_ values that are generated by the [Acceleration Bands](../../indicator/volatility/README.md#acceleration-bands-ab) indicator function to provide a _BUY_ action when closing is greather than or equals to _upperBand_, a _SELL_ action otherwise.

```TypeScript
import { abStrategy } from 'indicatorts';

const defaultConfig = { period: 20, multiplier: 4 };
const actions = abStrategy(asset, defaultConfig);

// Alternatively:
// const actions = accelerationBandsStrategy(asset, defaultConfig);
```

## Bollinger Bands Strategy

The [bollingerBandsStrategy](./bollingerBandsStrategy.ts) uses the _upperBand_, and _lowerBand_ values that are generated by the [Bollinger Bands](../../indicator/volatility/README.md#bollinger-bands-bb) indicator function to provide a _SELL_ action when the asset's closing is above the _upperBand_, and a _BUY_ action when the asset's closing is below the _lowerBand_ values, a _HOLD_ action otherwise.

```TypeScript
import { bbStrategy } from 'indicatorts';

const defaultConfig = { period: 20 };
const actions = bbStrategy(asset, defaultConfig);

// Alternatively:
// const actions = bollingerBandsStrategy(asset, defaultConfig);
```

## Projection Oscillator Strategy

The [projectionOscillatorStrategy](./projectionOscillatorStrategy.ts) uses _po_ and _spo_ values that are generated by the [Projection Oscillator (PO)](../../indicator/volatility/README.md#projection-oscillator-po) indicator function to provide a BUY action when _po_ is above _spo_, and SELL action when _po_ is below _spo_.

```TypeScript
import { poStrategy } from 'indicatorts';

const defaultConfig = { period: 14, smooth: 3 };
const actions = poStrategy(asset, defaultConfig);

// Alternatively:
// const actions = projectionOscillatorStrategy(asset, defaultConfig);
```

## Disclaimer

The information provided on this project is strictly for informational purposes and is not to be construed as advice or solicitation to buy or sell any security.

## License

Copyright (c) 2022 Onur Cinar. All Rights Reserved.

The source code is provided under MIT License.
