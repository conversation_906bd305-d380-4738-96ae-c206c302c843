export default class DB {
  dbName: string;
  // @ts-ignore
  db: IDBDatabase;
  // @ts-ignore
  store: IDBObjectStore;
  forms: string[] = ["excharge"];
  constructor(dbName: string) {
    this.dbName = dbName;
    // @ts-ignore
    // this.initDB()
  }
  async connect(): Promise<DB> {
    let that = this;
    return new Promise((resolve, reject) => {
      let request = window.indexedDB.open(this.dbName);
      request.onsuccess = function (event) {
        that.db = request.result;
        console.log("数据库打开成功");
        resolve(that);
      };
      request.onupgradeneeded = function (event) {
        let db = request.result;

        that.forms.map((value) => {
          db.createObjectStore(value, { autoIncrement: true });
        });
        console.log("数据库升级成功");
        resolve(that);
      };
    });
  }
  form(form: string) {
    this.store = this.db.transaction([form], "readwrite").objectStore(form);
    return this;
  }
  async add(data: any) {
    return await new Promise((resolve, reject) => {
      let request = this.store.add(data);
      request.onsuccess = (e) => {
        resolve(request.result);
      };
    });
  }
  async get(key: any) {
    return await new Promise((resolve, reject) => {
      let request = this.store.get(key);
      request.onsuccess = (e) => {
        resolve(request.result);
      };
    });
  }
}
