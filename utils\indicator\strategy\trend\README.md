# Trend Strategies

Trend strategies generate signals based on a trend indicator.

- [Trend Strategies](#trend-strategies)
  - [Absolute Price Oscillator Strategy](#absolute-price-oscillator-strategy)
  - [Aroon Strategy](#aroon-strategy)
  - [Balance of Power Strategy](#balance-of-power-strategy)
  - [Chande Forecast Oscillator Strategy](#chande-forecast-oscillator-strategy)
  - [KDJ Strategy](#kdj-strategy)
  - [MACD Strategy](#macd-strategy)
  - [Parabolic SAR Strategy](#parabolic-sar-strategy)
  - [Typical Price Strategy](#typical-price-strategy)
  - [Volume Weighted Moving Average (VWMA) Strategy](#volume-weighted-moving-average-vwma-strategy)
  - [Vortex Strategy](#vortex-strategy)
  - [Disclaimer](#disclaimer)
  - [License](#license)

**NOTE:** All configuration objects for all strategies are optional. If no configuration object is passed, the default configuration will be used. Likewise, you may also partially pass a configuration object, and the default values will be used for the missing properties.

## Absolute Price Oscillator Strategy

The [absolutePriceOscillatorStrategy](./absolutePriceOscillatorStrategy.ts) uses the values that are generated by the [Absolute Price Oscillator (APO)](../../indicator/trend/README.md#absolute-price-oscillator-apo) indicator to provide a _BUY_ action when the _AO_ is greather than zero, and _SELL_ action when _AO_ is less than zero, otherwise _HOLD_ action.

```TypeScript
import { apoStrategy } from 'indicatorts';

const defaultConfig = { fast: 14, slow: 30 };
const actions = apoStrategy(asset, defaultConfig);

// Alternatively:
// const actions = absolutePriceOscillatorStrategy(asset, defaultConfig);
```

## Aroon Strategy

The [aroonStrategy](./aroonStrategy.ts) uses the values that are generated by the [Aroon Indicator](../../indicator/trend/README.md#aroon) to provide a _BUY_ action when the _up_ is greather than _down_, and _SELL_ action when _up_ is less than _down_, otherwise _HOLD_ action.

```TypeScript
import { aroonStrategy } from 'indicatorts';

const defaultConfig = { period: 25 };
const actions = aroonStrategy(asset, defaultConfig);
```

## Balance of Power Strategy

The [balanceOfPowerStrategy](./balanceOfPowerStrategy.ts) uses the values that are generated by the [Balance of Power (BOP)](../../indicator/trend/README.md#balance-of-power-bop) indicator to provide a _BUY_ action when the _BOP_ is greather than zero, and _SELL_ action when _BOP_ is less than zero, otherwise _HOLD_ action.

```TypeScript
import { bopStrategy } from 'indicatorts';

const actions = bopStrategy(asset);

// Alternatively:
// const actions = balanceOfPowerStrategy(asset);
```

## Chande Forecast Oscillator Strategy

The [chandeForecastOscillatorStrategy](./chandeForecastOscillatorStrategy.ts) uses _cfo_ values that are generated by the [Chande Forecast Oscillator (CFO)](../../indicator/trend/README.md#chande-forecast-oscillator-cfo) indicator function to provide a BUY action when _cfo_ is below zero, and SELL action when _cfo_ is above zero.

```TypeScript
import { cfoStrategy } from 'indicatorts';

const actions = cfoStrategy(asset);

// Alternatively:
// const actions = chandeForecastOscillatorStrategy(asset);
```

## KDJ Strategy

The [kdjStrategy](./kdjStrategy.ts) function uses the _k_, _d_, _j_ values that are generated by the [Random Index (KDJ)](../../indicator/trend/README.md#random-index-kdj) indicator function to provide a BUY action when _k_ crosses above _d_ and _j_. It is stronger when below 20%. Also the SELL action is when _k_ crosses below _d_ and _j_. It is strong when above 80%.

```TypeScript
import { kdjStrategy } from 'indicatorts';

const defaultConfig = { rPeriod: 9, kPeriod: 3, dPeriod: 3 };
const actions = kdjStrategy(asset, defaultConfig);
```

## MACD Strategy

The [macdStrategy](./macdStrategy.ts) uses the _macd_, and _signal_ values that are generated by the [Moving Average Convergence Divergence (MACD)](../../indicator/trend/README.md#moving-average-convergence-divergence-macd) indicator function to provide a BUY action when _macd_ crosses above _signal_, and SELL action when _macd_ crosses below _signal_.

```TypeScript
import { macdStrategy } from 'indicatorts';

const defaultConfig = { fast: 12, slow: 26, signal: 9 };
const actions = macdStrategy(asset, defaultConfig);

// Alternatively:
// const actions = movingAverageConvergenceDivergenceStrategy(asset, defaultConfig);
```

## Parabolic SAR Strategy

The [parabolicSARStrategy](./parabolicSarStrategy.ts) uses the values that are generated by the [Parabolic SAR](../../indicator/trend/README.md#parabolic-sar-psar) indicator function to provide a _BUY_ action when the trend is _FALLING_, and _SELL_ action when the trend is _RISING_, and _HOLD_ action when the trend is _STABLE_.

```TypeScript
import { psarStrategy } from 'indicatorts';

const defaultConfig = { step: 0.02, max: 0.2 };
const actions = psarStrategy(asset, defaultConfig);

// Alternatively:
// const actions = parabolicSARStrategy(asset, defaultConfig);
```

## Typical Price Strategy

The [typicalPriceStrategy](./typicalPriceStrategy.ts) uses the values that are generated by the [Typical Price](../../indicator/trend/README.md#typical-price) indicator function to provide a _BUY_ action when the value is greather than the previous value, and _SELL_ action when the value is less than the previous value, and _HOLD_ action when value is equal to the previous value.

```TypeScript
import { typpriceStrategy } from 'indicatorts';

const actions = typpriceStrategy(asset);

// Alternatively:
// const actions = typicalPriceStrategy(asset);
```

## Volume Weighted Moving Average (VWMA) Strategy

The [vwmaStrategy](./vwmaStrategy.ts) function uses SMA and VWMA indicators to provide a _BUY_ action when VWMA is above SMA, and a _SELL_ signal when VWMA is below SMA, a _HOLD_ signal otherwse.

```TypeScript
import { vwmaStrategy } from 'indicatorts';

const defaultConfig = { period: 20 };
const actions = vwmaStrategy(asset, defaultConfig);

// Alternatively:
// const actions = volumeWeightedMovingAverageStrategy(asset, defaultConfig);
```

## Vortex Strategy

The [vortexStrategy](./vortexStrategy.ts) uses the values that are generated by the [Vortex Indicator](../../indicator/trend/README.md#vortex-indicator) indicator function to provide a _BUY_ action when the _plusVi_ is greather than the _minusVi_, and _SELL_ action when the _plusVi_ is less than the _minusVi_, and _HOLD_ action when the _plusVi_ is equal to the _minusVi_.

```TypeScript
import { vortexStrategy } from 'indicatorts';

const defaultConfig = { period: 14 };
const actions = vortexStrategy(asset, defaultConfig);
```

## Disclaimer

The information provided on this project is strictly for informational purposes and is not to be construed as advice or solicitation to buy or sell any security.

## License

Copyright (c) 2022 Onur Cinar. All Rights Reserved.

The source code is provided under MIT License.
