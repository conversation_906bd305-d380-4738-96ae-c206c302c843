{"name": "hhh", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@vitejs/plugin-basic-ssl": "^2.0.0", "big.js": "^6.2.2", "ccxt": "^4.4.90", "dexie": "^4.0.9", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.5", "lightweight-charts": "^5.0.7", "localforage": "^1.10.0", "object-sizeof": "^2.6.5", "socks-proxy-agent": "^8.0.4", "uuid": "^11.0.3", "vue": "^3.5.12", "vue-router": "4"}, "devDependencies": {"@types/big.js": "^6.2.2", "@vitejs/plugin-vue": "^5.1.4", "autoprefixer": "^10.4.20", "less": "^4.2.0", "less-loader": "7.3.0", "postcss": "^8.4.48", "tailwindcss": "^3.4.14", "typescript": "~5.6.2", "vite": "^5.4.10", "vue-tsc": "^2.1.8"}}