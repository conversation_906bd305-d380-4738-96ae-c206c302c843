import { Bee } from "./bee";
import { Hive } from "./hive";
import { CallBackFunc, OHLCVLst } from "./type";
import { buildOHLCV } from "./functions/ohlcv";

export class Squad {
  hive: Hive;
  baseBee: Bee;
  lst: Bee[] = [];
  constructor(coinName: string, hive: Hive) {
    this.hive = hive;
    this.baseBee = new Bee(coinName, hive);
  }
  async fetchOhlcv(
    startTime: number,
    intervalTime: number,
    callBack?: CallBackFunc
  ) {
    return this.baseBee.fetchOHLCV(startTime, intervalTime, callBack);
  }
  setOhlcv(ohlcv: OHLCVLst) {
    this.baseBee.setOHLCV(ohlcv, 1);
  }
  buildOHLCV(interval: number): Bee {
    let bee = this.baseBee.buildBee(interval);
    this.lst.push(bee);
    return bee;
  }
  buildOHLCVs(...intervals: number[]) {
    return intervals.map((interval) => {
      return this.buildOHLCV(interval);
    });
  }
}
