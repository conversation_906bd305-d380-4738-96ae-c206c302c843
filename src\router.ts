/*
 * @Author: zgr126 <EMAIL>
 * @Date: 2024-11-04 21:43:59
 * @LastEditors: zgr126 <EMAIL>
 * @LastEditTime: 2025-06-23 01:02:30
 * @FilePath: \hhh\src\router.ts
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
 */
import { createWebHashHistory, createRouter } from 'vue-router'

// import testPage from './pages/test.vue'
import exchangeLst from './pages/exchangeLst.vue'
import chart from './pages/chart.vue'
const routes = [
  {
    path: '/',
    redirect: '/chart',
  },
  { path: '/exchangeLst', component: exchangeLst },
  { path: '/chart', component: chart },
 
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

router.beforeEach((to, from, next) => {
  console.log(to);
  console.log(from);
  // 确保不会阻止子路径的导航
  next();
});
export default router;
