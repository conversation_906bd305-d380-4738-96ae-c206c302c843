import { OHLCVLst } from "@utils/v2/type";
import {
  BarPrice,
  BaselineData,
  BaselineSeriesPartialOptions,
  CandlestickData,
  CandlestickSeriesPartialOptions,
  ChartOptions,
  createChart,
  DeepPartial,
  IChartApi,
  ISeriesApi,
  LineData,
  LineSeriesPartialOptions,
  LogicalRange,
  LogicalRangeChangeEventHandler,
  MouseEventHandler,
  MouseEventParams,
  OhlcData,
  SingleValueData,
  Time,
  WhitespaceData,
} from "lightweight-charts";
import { v7 as uuid } from "uuid";
import { option, Petal } from "./petal";
// lightChart 配置
const chartOptions: DeepPartial<ChartOptions> = {
  autoSize: true,
  // width: 0,
  // height: 0,
  layout: {
    textColor: "black",
    background: {
      type: "solid" as any,
      color: "white",
    },
    attributionLogo: false,
  },
  timeScale: {
    timeVisible: true,
    secondsVisible: true,
  },
  localization: {
    timeFormatter: (businessDayOrTimestamp: number) => {
      const t = businessDayOrTimestamp * 1000;
      let d: Date = new Date();
      d.format("yyyy-MM-dd hh:mm:ss");
      const str = new Date(t).format("yy/M/d hh:mm:ss");
      // console.log("time:", new Date().getTime(), "ltime:", t);
      return str;
    },
  },
  rightPriceScale: {
    autoScale: true,
    minimumWidth: 120,
    entireTextOnly: true,
    mode: 0,
    // scaleMargins: {
    //   top: 0.3,
    //   bottom: 0.25,
    // },
  },
  crosshair: {
    mode: 0,
  },
};

export class Flower {
  id: string = uuid();
  chart: IChartApi | undefined;
  interval: number = 1;
  man: FlowerMan;
  baseData: OHLCVLst = [];
  petals: Petal[] = [];
  constructor(man: FlowerMan) {
    this.man = man;
  }
  createChart(interval: number) {
    this.interval = interval;
    this.chart = createChart(this.id, chartOptions);
    this.chart
      .timeScale()
      .subscribeVisibleLogicalRangeChange((e) => this.LogicalRange(e));
    this.chart.subscribeCrosshairMove((e) => this.CrosshairMove(e));
    this.chart.subscribeClick((e) => {
      this.clickEvent(e);
    });
    // this.chart.timeScale().fitContent();
    // 设置时间格式化，因为只能获取价格，在此设置可以获取到chart
    this.chart.applyOptions({
      localization: {
        priceFormatter: (priceValue: BarPrice) => {
          let range = (this.chart as IChartApi)
            .timeScale()
            .getVisibleLogicalRange();
          if (range) {
            // && this.man.fock?.id === this.id
            let data = this.petals[0].series[0].data();
            let id = Math.floor(range.from);
            id = id < 0 ? 0 : id;
            id = id > data.length - 1 ? data.length - 1 : id;
            let v = data[id] as OhlcData;
            let v2 = data[id] as SingleValueData;
            let d = v.close || v2.value;
            let percent = Number((d / priceValue - 1) * 100).toFixed(2);
            return `${-percent}% (${priceValue.toPrecision(5)})`;
          }
          return "";
        },
      },
    });
  }
  setData(data: OHLCVLst) {
    this.baseData = data;
  }
  addPetal(petal: string, options?: option) {
    const _petal = new Petal(this, petal, options);
    this.petals.push(_petal);
  }
  // 点击
  clickEvent(event: MouseEventParams<Time>) {
    this.man.clickEvent(event);
  }
  // 范围改变
  LogicalRange(event: LogicalRange | null) {
    if (event) {
      this.man.LogicalRange(this, event);
    }
  }
  // 鼠标移动
  CrosshairMove(event: MouseEventParams<Time>) {
    if (event.time) {
      this.man.CrosshairMove(this, event);
    }
  }
}

type chartConfig = {
  asyncDiffrentInterval: false | "quel" | "ratio";
};

export class FlowerMan {
  flowers: Flower[] = [];
  fock: Flower | null = null;
  mousePosition: number = 0;
  fockTime: number = 0;
  chartConfig: chartConfig = {
    asyncDiffrentInterval: "ratio",
  };
  flower() {
    let flower = new Flower(this);
    this.flowers.push(flower);
    return flower;
  }
  CrosshairMove(flower: Flower, event: MouseEventParams<Time>) {
    if (!event.sourceEvent) {
      return;
    }
    this.fockTime = event.time ? Number(event.time) : 0;
    this.fock = flower;
    if (event.sourceEvent?.clientX) {
      this.mousePosition = event.sourceEvent?.clientX as number;
    }

    let handle = () => {
      this.flowers.map((e) => {
        if (e.id === this.fock?.id) {
          return;
        }
        if (e.interval === flower.interval) {
          (e.chart as IChartApi).setCrosshairPosition(
            0,
            event.time as Time,
            e.petals[0].series[0]
          );
        } else {
          let data = e.petals[0].series[0].data();
          let nowLogic = findLogicId(this.fockTime, data);
          if (nowLogic == -1) return;
          let time = data[nowLogic].time;
          (e.chart as IChartApi).setCrosshairPosition(
            0,
            time,
            e.petals[0].series[0]
          );
        }
      });
    };
    handle();
    // setTimeout(handle, 100);
  }
  clickEvent(event: MouseEventParams<Time>) {
    console.log(event);
  }
  LogicalRange(_flower: Flower, event: LogicalRange) {
    // 防止循环触发
    if (_flower.id !== this.fock?.id) return;
    // 同步不同时间间隔
    let asyncInterval = (
      source: Flower,
      flower: Flower,
      event: LogicalRange
    ) => {
      if (!this.mousePosition) return;
      if (this.chartConfig.asyncDiffrentInterval == false) return;

      let sourceTimeScale = (source.chart as IChartApi).timeScale();
      let sourcePosition = sourceTimeScale.getVisibleLogicalRange();
      let sourceLogic = sourceTimeScale.coordinateToLogical(this.mousePosition);
      let sourceTime = sourceTimeScale.coordinateToTime(this.mousePosition);
      if (!sourcePosition || !sourceLogic || !sourceTime) return;
      let nowLogic = findLogicId(
        sourceTime as number,
        flower.petals[0].series[0].data()
      );

      let left = sourceLogic - sourcePosition.from;
      let right = sourcePosition.to - sourceLogic;
      let ratio = this.chartConfig.asyncDiffrentInterval == "quel" ? 1 : 1;
      ratio =
        this.chartConfig.asyncDiffrentInterval == "ratio"
          ? source.interval / flower.interval
          : 1;
      let move = {
        from: nowLogic - left * ratio,
        to: nowLogic + right * ratio,
      };
      (flower.chart as IChartApi).timeScale().setVisibleLogicalRange(move);
    };
    this.flowers.map((e) => {
      if (e.id === this.fock?.id) {
        return;
      }
      if (e.interval === _flower.interval) {
        (e.chart as IChartApi).timeScale().setVisibleLogicalRange(event);
      } else {
        asyncInterval(_flower, e, event);
      }
    });
  }
}

interface tempTime {
  time: number;
}
let findLogicId = (
  time: number,
  timeLst: readonly (
    | CandlestickData<Time>
    | WhitespaceData<Time>
    | BaselineData<Time>
    | LineData<Time>
  )[]
) => {
  let id = timeLst.findIndex((e, index) => {
    return time <= Number(e.time);
  });
  return id;
};
