<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import ccxt from 'ccxt'

interface MarketData {
  symbol: string
  base: string
  quote: string
  active: boolean
  baseVolume: number
  quoteVolume: number
  lastPrice: number
  usdtVolume: number
  type: 'future'
}

const marketData = ref<MarketData[]>([])

async function fetchMarketData() {
  try {
    // 创建币安合约交易所实例
    const futureExchange = new ccxt.binanceusdm()
    
    // 加载市场数据
    const futureMarkets = await futureExchange.loadMarkets()
    
    // 获取24小时交易量数据
    const futureTickers = await futureExchange.fetchTickers()
    
    // 处理合约市场数据
    marketData.value = Object.values(futureMarkets)
      .filter(market => market.active)
      .map(market => {
        const ticker = futureTickers[market.symbol]
        return {
          symbol: market.symbol,
          base: market.base,
          quote: market.quote,
          active: market.active,
          baseVolume: ticker?.baseVolume || 0,
          quoteVolume: ticker?.quoteVolume || 0,
          lastPrice: ticker?.last || 0,
          usdtVolume: ticker?.quoteVolume || 0, // 合约市场直接使用USDT计价
          type: 'future' as const
        }
      })
      .sort((a, b) => b.usdtVolume - a.usdtVolume)
  } catch (error) {
    console.error('获取市场数据失败:', error)
  }
}

onMounted(() => {
  fetchMarketData()
})

// 格式化交易量显示
function formatVolume(volume: number): string {
  if (volume >= 1_0000_0000) {
    return (volume / 1_0000_0000).toFixed(3) + '亿'
  } else if (volume >= 1_0000) {
    return (volume / 1_0000).toFixed(4) + '万'
  } else {
    return volume.toFixed(2)
  }
}
</script>

<template>
  <div class="p-4">
    <h1 class="text-2xl font-bold mb-4">币安合约市场数据（按24小时USDT交易额排序）</h1>
    
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white border border-gray-300">
        <thead>
          <tr class="bg-gray-100">
            <th class="px-4 py-2 border">序号</th>
            <th class="px-4 py-2 border">交易对</th>
            <th class="px-4 py-2 border">基础货币</th>
            <th class="px-4 py-2 border">计价货币</th>
            <th class="px-4 py-2 border">最新价格</th>
            <th class="px-4 py-2 border">24小时交易量(USDT)</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(market, index) in marketData" :key="index" class="hover:bg-gray-50">
            <td class="px-4 py-2 border">{{ index + 1 }}</td>
            <td class="px-4 py-2 border">{{ market.symbol }}</td>
            <td class="px-4 py-2 border">{{ market.base }}</td>
            <td class="px-4 py-2 border" :style="{'color': market.quote === 'USDT' ? 'green' : 'red'}">{{ market.quote }}</td>
            <td class="px-4 py-2 border">{{ market.lastPrice.toFixed(8) }}</td>
            <td class="px-4 py-2 border">{{ formatVolume(market.usdtVolume) }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped>
/* 可以根据需要添加自定义样式 */
</style>