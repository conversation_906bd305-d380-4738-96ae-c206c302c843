import { Exchange, OHLCV } from "ccxt";
import { CallBackFunc, OHLCVLst } from "../type";
import { symbolName } from "../../hhh2/type";
import Big from "big.js";

export function SymbolName(name: string) {
  let isSymbol = name.indexOf("/");
  if (isSymbol > -1) {
    return name;
  }
  let _name = name.toLocaleUpperCase();
  return _name + "/USDT";
}

// 构建不同时间线段数据
export function buildOHLCV(ohlcvLst: OHLCVLst, interval: number): OHLCV[] {
  if (interval == 1) {
    throw new Error("interval must not be 1");
  }
  let num = Math.ceil(ohlcvLst.length / interval);
  return new Array(num).fill(0).map((_, i) => {
    const start = i * interval;
    const end = start + interval;
    return mergeOHLCV(ohlcvLst.slice(start, end));
  });
}

// 基础获取OHLCV数据
export async function getOHLCVs(
  symbolName: string,
  exchange: Exchange,
  startTime: number,
  intervalTime: number,
  callBack?: CallBackFunc
): Promise<OHLCV[]> {
  let _symbol = symbolName;
  let _startTime = calcStartTime(startTime);
  let _intervalTime = forSecondTime(intervalTime);
  let _endTime = _startTime + _intervalTime;
  let allNum = Math.floor(_intervalTime / 1000 / 1000);
  allNum = allNum === 0 ? 1 : allNum;
  let result: OHLCVLst = [];
  let promiseLst: Promise<OHLCV[]>[] = [];
  for (let i = 0; i < allNum + 1; i++) {
    let ohlcv = exchange.fetchOHLCV(
      _symbol,
      "1s",
      _startTime + i * 1000 * 1000,
      1000
    );
    promiseLst.push(ohlcv);
    // result.push(ohlcv[0]);
  }
  await Promise.all(promiseLst).then((valueLst) => {
    valueLst.forEach((e) => {
      result.push(...e);
    });
  });
  return result;
}
// 合并ohlcv数据基础函数
function mergeOHLCV(ohlcvLst: OHLCVLst): OHLCV {
  let result: OHLCV = [0, 0, 0, 0, 0, 0];
  ohlcvLst.forEach((item, index, lst) => {
    if (index === 0) {
      result[0] = item[0];
      result[1] = item[1];
      result[2] = item[2];
      result[3] = item[3];
    } else {
      result[2] = Math.max(result[2] as number, item[2] as number);
      result[3] = Math.min(result[3] as number, item[3] as number);
    }
    if (index == lst.length - 1) {
      result[4] = item[4];
    }
    result[5] = new Big(result[5] as number).plus(item[5] as number).toNumber();
  });
  return result;
}
function calcStartTime(time: number) {
  return forSecondTime(time) - 1;
}
function calcEndTime(time: number) {
  return forSecondTime(time) + 1;
}
function forSecondTime(time: number) {
  return Math.floor(time / 1000) * 1000;
}
