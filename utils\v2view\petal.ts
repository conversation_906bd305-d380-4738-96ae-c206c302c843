import { OHLCVLst } from "@utils/v2/type";
import { BaselineData, IChartApi, ISeriesApi, Time } from "lightweight-charts";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "heiki<PERSON><PERSON>";
import type { Flower } from "./flower";
import { supertrend } from "supertrend"; //from "@utils/indicator/custom/supertrend";
import { v7 as uuid } from "uuid";
export type option = {
  [key: string]: number | string | number[];
};

export class Petal {
  flower: Flower;
  series: ISeriesApi<"Candlestick" | "Baseline" | "Line">[] = [];
  group!: Map<string, ISeriesApi<"Candlestick" | "Baseline" | "Line">[]>;
  id: string = uuid();
  constructor(flower: Flower, name: string, options?: option) {
    this.flower = flower;
    switch (name) {
      case "candle":
        addOHLCV(this, options);
        break;
      case "supertrend":
        addSuperTrend(this, options);
      default:
        break;
    }
  }
  // addCandlestickSeries(candlestickOptions?: CandlestickSeriesPartialOptions) {
  //   let ser = this.chart.addCandlestickSeries(candlestickOptions);
  //   this.series.push(ser);
  //   return ser;
  // }
  // addBaselineSeries(baselineOptions?: BaselineSeriesPartialOptions) {
  //   if (baselineOptions) {
  //     baselineOptions.lineWidth = 1;
  //   }
  //   let ser = this.chart.addBaselineSeries(baselineOptions);
  //   this.series.push(ser);
  //   return ser;
  // }
  // addLineSeries(lineOptions?: LineSeriesPartialOptions) {
  //   let ser = this.chart.addLineSeries(lineOptions);
  //   this.series.push(ser);
  //   return ser;
  // }
}
function addSuperTrend(petal: Petal, options?: option) {
  let period = 10;
  let multiplier = 3;
  let data = petal.flower.baseData;
  let _data = data.map((e) => {
    return {
      time: e[0] as Time,
      open: e[1] as number,
      high: e[2] as number,
      low: e[3] as number,
      close: e[4] as number,
    };
  });
  // let _s = supertrend(_data, multiplier, period);
  let _s = supertrend({
    initialArray: _data,
    period: period,
    multiplier: multiplier,
  });
  let ser = (petal.flower.chart as IChartApi).addLineSeries({
    lineWidth: 1,
    color: "#333",
    // lineType: 2,
  });
  let r: BaselineData[] = [];
  console.log(_s);
  let __data = _s.slice(1).map((e, index) => {
    // if (index % 3 == 0) {
    r.push({
      time: (Number(_data[index + 1 + multiplier].time) / 1000) as Time,
      value: e,
    });
    // }
    // return {
    //   time: Number(_data[index+1+period].time)/1000 as Time,
    //   value: e
    // }
  });
  ser.setData(r);
  // let buys: BaselineData[][] = []
  // let sells: BaselineData[][] = []
  // let nowData: BaselineData[] = []
  // let nowSate: ''|'buy' |'sell' = ''
  // _s.slice(1).map((e, index) => {
  //   let data = _data[index + 1+period]
  //   let time = Number(data.time) / 1000
  //   let _nowSate: ''|'buy' |'sell' = data.close > e? 'buy':'sell'
  //   console.log(_nowSate)
  //   if (nowSate !== _nowSate){
  //     switch(nowSate){
  //       case 'buy':
  //         buys.push(nowData)
  //         break
  //       case 'sell':
  //         sells.push(nowData)
  //         break
  //       case '':
  //         break
  //     }
  //     nowData = []
  //     nowSate = _nowSate
  //   }else{

  //     nowData.push({
  //       time: time as Time,
  //       value: e
  //     })
  //   }

  // })
  // buys.map(item=>{
  //   let ser = petal.flower.chart.addLineSeries({lineWidth:1, color: '#333'})
  //   console.log(item)
  //   ser.setData(item)
  // })
  // sells.map(item=>{
  //   let ser = petal.flower.chart.addLineSeries({lineWidth:1, color: '#f00'})
  //   ser.setData(item)
  // })
}
function addOHLCV(petal: Petal, options?: option) {
  let chart = petal.flower.chart as IChartApi;
  let ser = chart.addCandlestickSeries();
  let data = petal.flower.baseData;
  let _data = data.map((e) => {
    return {
      time: (Number(e[0]) / 1000) as Time,
      open: e[1],
      high: e[2],
      low: e[3],
      close: e[4],
    };
  });

  if (options?.type === "heikinashi") {
    _data = HeikinAshi(_data, {
      overWrite: true, //overwrites the original data or create a new array
      formatNumbers: false, //formats the numbers and reduces their significant digits based on the values
      decimals: 4, //number of significant digits
      forceExactDecimals: false, //force the number of significant digits or reduce them if the number is high
    });
  }

  ser.setData(_data);
  petal.series.push(ser);
}
