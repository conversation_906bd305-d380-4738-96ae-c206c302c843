<!--
 * @Author: zgr126 <EMAIL>
 * @Date: 2025-07-09
 * @Description: 主图表页面 - TradingView风格的交易界面
 *
 * Copyright (c) 2025 by zgr126, All Rights Reserved.
-->
<template>
  <div class="trading-view-container">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <select v-model="selectedExchange" @change="onExchangeChange" class="exchange-selector">
          <option value="binance">Binance</option>
          <option value="okx">OKX</option>
          <option value="bybit">Bybit</option>
        </select>

        <div class="symbol-search">
          <input
            v-model="symbolSearch"
            @input="onSymbolSearch"
            placeholder="搜索交易对..."
            class="symbol-input"
          />
          <div v-if="symbolSuggestions.length > 0" class="symbol-suggestions">
            <div
              v-for="symbol in symbolSuggestions"
              :key="symbol"
              @click="selectSymbol(symbol)"
              class="symbol-suggestion"
            >
              {{ symbol }}
            </div>
          </div>
        </div>

        <div class="current-symbol">{{ currentSymbol }}</div>
      </div>

      <div class="toolbar-center">
        <div class="timeframe-selector">
          <button
            v-for="tf in timeframes"
            :key="tf.value"
            @click="selectTimeframe(tf.value)"
            :class="['timeframe-btn', { active: currentTimeframe === tf.value }]"
          >
            {{ tf.label }}
          </button>
        </div>
      </div>

      <div class="toolbar-right">
        <button @click="toggleIndicatorPanel" class="indicator-btn">
          指标
        </button>
        <button @click="toggleSettings" class="settings-btn">
          设置
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧面板 -->
      <div v-if="showIndicatorPanel" class="left-panel">
        <div class="panel-header">
          <h3>技术指标</h3>
          <button @click="toggleIndicatorPanel" class="close-btn">×</button>
        </div>

        <div class="indicator-categories">
          <div class="category">
            <h4>趋势指标</h4>
            <div class="indicator-list">
              <div
                v-for="indicator in trendIndicators"
                :key="indicator.name"
                @click="addIndicator(indicator)"
                class="indicator-item"
              >
                {{ indicator.label }}
              </div>
            </div>
          </div>

          <div class="category">
            <h4>震荡指标</h4>
            <div class="indicator-list">
              <div
                v-for="indicator in oscillatorIndicators"
                :key="indicator.name"
                @click="addIndicator(indicator)"
                class="indicator-item"
              >
                {{ indicator.label }}
              </div>
            </div>
          </div>

          <div class="category">
            <h4>自定义指标</h4>
            <button @click="openCustomIndicatorEditor" class="add-custom-btn">
              + 添加自定义指标
            </button>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="chart-container" :class="{ 'with-panel': showIndicatorPanel }">
        <!-- 主图表 -->
        <div class="main-chart">
          <div ref="mainChartRef" class="chart-element" :id="mainChartId"></div>

          <!-- 图表上的指标控制 -->
          <div class="chart-indicators">
            <div
              v-for="indicator in activeIndicators.filter(i => i.overlay)"
              :key="indicator.id"
              class="indicator-control"
            >
              <span>{{ indicator.name }}</span>
              <button @click="configureIndicator(indicator)" class="config-btn">⚙</button>
              <button @click="removeIndicator(indicator.id)" class="remove-btn">×</button>
            </div>
          </div>
        </div>

        <!-- 子图表区域 -->
        <div class="sub-charts">
          <div
            v-for="indicator in activeIndicators.filter(i => !i.overlay)"
            :key="indicator.id"
            class="sub-chart"
          >
            <div class="sub-chart-header">
              <span>{{ indicator.name }}</span>
              <div class="sub-chart-controls">
                <button @click="configureIndicator(indicator)" class="config-btn">⚙</button>
                <button @click="removeIndicator(indicator.id)" class="remove-btn">×</button>
              </div>
            </div>
            <div :ref="`subChart_${indicator.id}`" class="chart-element" :id="`sub_chart_${indicator.id}`"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义指标编辑器模态框 -->
    <div v-if="showCustomEditor" class="modal-overlay" @click="closeCustomEditor">
      <div class="custom-editor-modal" @click.stop>
        <div class="modal-header">
          <h3>自定义指标编辑器</h3>
          <button @click="closeCustomEditor" class="close-btn">×</button>
        </div>

        <div class="editor-content">
          <div class="editor-toolbar">
            <button @click="saveCustomIndicator" class="save-btn">保存</button>
            <button @click="testCustomIndicator" class="test-btn">测试</button>
          </div>

          <textarea
            v-model="customIndicatorCode"
            class="code-editor"
            placeholder="// 在这里编写你的自定义指标代码
// 示例：简单移动平均线
//@version=5
indicator('My SMA', overlay=true)
length = input(20, 'Length')
sma_value = ta.sma(close, length)
plot(sma_value, color=color.blue, title='SMA')"
          ></textarea>
        </div>
      </div>
    </div>

    <!-- 设置面板 -->
    <div v-if="showSettings" class="settings-panel">
      <div class="panel-header">
        <h3>设置</h3>
        <button @click="toggleSettings" class="close-btn">×</button>
      </div>

      <div class="settings-content">
        <div class="setting-group">
          <h4>图表设置</h4>
          <label>
            <input type="checkbox" v-model="chartSettings.showVolume" @change="updateChartSettings">
            显示成交量
          </label>
          <label>
            <input type="checkbox" v-model="chartSettings.showGrid" @change="updateChartSettings">
            显示网格
          </label>
        </div>

        <div class="setting-group">
          <h4>数据设置</h4>
          <label>
            自动刷新间隔 (秒):
            <input
              type="number"
              v-model="chartSettings.refreshInterval"
              @change="updateRefreshInterval"
              min="1"
              max="300"
            >
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { marketDataService, MarketDataConfig } from '@/services/MarketDataService'
import { ChartService } from '@/services/ChartService'
import { IndicatorService, IndicatorConfig } from '@/services/IndicatorService'
import { v4 as uuidv4 } from 'uuid'
import { OHLCV } from 'ccxt'

// 响应式数据
const selectedExchange = ref('binance')
const symbolSearch = ref('')
const symbolSuggestions = ref<string[]>([])
const currentSymbol = ref('BTC/USDT')
const currentTimeframe = ref('1h')
const showIndicatorPanel = ref(false)
const showSettings = ref(false)
const showCustomEditor = ref(false)
const customIndicatorCode = ref('')

// 图表相关
const mainChartRef = ref<HTMLElement>()
const mainChartId = ref(`main_chart_${uuidv4()}`)
const chartService = ref<ChartService>()

// 时间周期选项
const timeframes = ref([
  { label: '1m', value: '1m' },
  { label: '5m', value: '5m' },
  { label: '15m', value: '15m' },
  { label: '1h', value: '1h' },
  { label: '4h', value: '4h' },
  { label: '1d', value: '1d' },
  { label: '1w', value: '1w' }
])

// 指标数据
const trendIndicators = ref([
  { name: 'sma', label: '简单移动平均线 (SMA)', overlay: true },
  { name: 'ema', label: '指数移动平均线 (EMA)', overlay: true },
  { name: 'bollinger', label: '布林带 (Bollinger Bands)', overlay: true },
  { name: 'ichimoku', label: '一目均衡表 (Ichimoku)', overlay: true }
])

const oscillatorIndicators = ref([
  { name: 'rsi', label: '相对强弱指数 (RSI)', overlay: false },
  { name: 'macd', label: 'MACD', overlay: false },
  { name: 'stoch', label: '随机指标 (Stochastic)', overlay: false },
  { name: 'cci', label: '商品通道指数 (CCI)', overlay: false }
])

const activeIndicators = ref<any[]>([])

// 图表设置
const chartSettings = reactive({
  showVolume: true,
  showGrid: true,
  refreshInterval: 30
})

// 市场数据
const currentOHLCV = ref<OHLCV[]>([])
const dataSubscription = ref<(() => void) | null>(null)

// 计算属性
const availableSymbols = computed(() => {
  return marketDataService.getSymbols(selectedExchange.value, { active: true })
})

// 辅助函数
const getIndicatorColor = (indicatorName: string): string => {
  const colors: Record<string, string> = {
    sma: '#2196f3',
    ema: '#ff9800',
    rsi: '#9c27b0',
    macd: '#4caf50',
    bollinger: '#f44336',
    stoch: '#00bcd4',
    cci: '#795548'
  }
  return colors[indicatorName] || '#2196f3'
}

// 方法定义
const loadMarketData = async () => {
  // 市场数据由 marketDataService 管理，这里只需要触发符号列表更新
  // 符号列表通过 computed 属性自动更新
}

const onExchangeChange = async () => {
  await loadMarketData()
  await loadChartData()
}

const onSymbolSearch = () => {
  if (symbolSearch.value.length < 2) {
    symbolSuggestions.value = []
    return
  }

  symbolSuggestions.value = availableSymbols.value
    .filter(symbol =>
      symbol.toLowerCase().includes(symbolSearch.value.toLowerCase())
    )
    .slice(0, 10)
}

const selectSymbol = (symbol: string) => {
  currentSymbol.value = symbol
  symbolSearch.value = ''
  symbolSuggestions.value = []
  loadChartData()
}

const selectTimeframe = (timeframe: string) => {
  currentTimeframe.value = timeframe
  loadChartData()
}

const loadChartData = async () => {
  try {
    // 取消之前的订阅
    if (dataSubscription.value) {
      dataSubscription.value()
      dataSubscription.value = null
    }

    const config: MarketDataConfig = {
      exchange: selectedExchange.value,
      symbol: currentSymbol.value,
      timeframe: currentTimeframe.value,
      limit: 500
    }

    // 获取初始数据
    const ohlcv = await marketDataService.fetchOHLCV(config)
    currentOHLCV.value = ohlcv

    // 更新图表
    updateChart(ohlcv)

    // 订阅实时更新
    dataSubscription.value = marketDataService.subscribe(config, (newOhlcv) => {
      currentOHLCV.value = newOhlcv
      updateChart(newOhlcv)
    })
  } catch (error) {
    console.error('加载图表数据失败:', error)
  }
}

const updateChart = (ohlcv: OHLCV[]) => {
  if (!chartService.value) return

  // 使用 ChartService 更新数据
  chartService.value.updateOHLCV(ohlcv)

  // 更新指标
  updateIndicators(ohlcv)
}

const updateIndicators = (ohlcv: OHLCV[]) => {
  if (!chartService.value || activeIndicators.value.length === 0) return

  activeIndicators.value.forEach(indicator => {
    const config: IndicatorConfig = {
      id: indicator.id,
      name: indicator.name,
      type: indicator.name as any,
      overlay: indicator.overlay,
      parameters: indicator.config || {}
    }

    try {
      const result = IndicatorService.calculateIndicator(ohlcv, config)

      // 如果指标系列不存在，创建它
      if (!chartService.value!.getIndicatorSeries(indicator.id)) {
        chartService.value!.addIndicatorSeries({
          id: indicator.id,
          type: indicator.overlay ? 'line' : 'line',
          title: indicator.label,
          overlay: indicator.overlay,
          color: getIndicatorColor(indicator.name),
          lineWidth: 2
        })
      }

      // 更新指标数据
      chartService.value!.updateIndicatorData(indicator.id, result.data)
    } catch (error) {
      console.error(`计算指标 ${indicator.name} 失败:`, error)
    }
  })
}

const toggleIndicatorPanel = () => {
  showIndicatorPanel.value = !showIndicatorPanel.value
}

const toggleSettings = () => {
  showSettings.value = !showSettings.value
}

const addIndicator = (indicator: any) => {
  const newIndicator = {
    id: uuidv4(),
    name: indicator.name,
    label: indicator.label,
    overlay: indicator.overlay,
    config: {}
  }

  activeIndicators.value.push(newIndicator)

  // 如果是子图表指标，创建新的图表
  if (!indicator.overlay) {
    nextTick(() => {
      createSubChart(newIndicator)
    })
  }

  // 计算并显示指标
  calculateIndicator(newIndicator)
}

const removeIndicator = (indicatorId: string) => {
  const index = activeIndicators.value.findIndex(i => i.id === indicatorId)
  if (index > -1) {
    activeIndicators.value.splice(index, 1)

    // 从图表中移除指标系列
    if (chartService.value) {
      chartService.value.removeIndicatorSeries(indicatorId)
    }
  }
}

const configureIndicator = (indicator: any) => {
  // 打开指标配置对话框
  console.log('配置指标:', indicator)
}

const createSubChart = (indicator: any) => {
  // 创建子图表
  console.log('创建子图表:', indicator)
}

const calculateIndicator = (indicator: any) => {
  // 计算指标值
  console.log('计算指标:', indicator)
}

const openCustomIndicatorEditor = () => {
  showCustomEditor.value = true
}

const closeCustomEditor = () => {
  showCustomEditor.value = false
  customIndicatorCode.value = ''
}

const saveCustomIndicator = () => {
  // 保存自定义指标
  console.log('保存自定义指标:', customIndicatorCode.value)
}

const testCustomIndicator = () => {
  // 测试自定义指标
  console.log('测试自定义指标:', customIndicatorCode.value)
}

const updateChartSettings = () => {
  // 更新图表设置
  if (chartService.value) {
    chartService.value.updateOptions({
      showVolume: chartSettings.showVolume,
      showGrid: chartSettings.showGrid
    })
  }
}

const updateRefreshInterval = () => {
  // 更新刷新间隔
  console.log('更新刷新间隔:', chartSettings.refreshInterval)
  // 重新订阅数据以应用新的刷新间隔
  loadChartData()
}

// 生命周期钩子
onMounted(async () => {
  // 初始化图表服务
  await nextTick()
  if (mainChartRef.value) {
    chartService.value = new ChartService({
      container: mainChartRef.value,
      theme: 'dark',
      showVolume: chartSettings.showVolume,
      showGrid: chartSettings.showGrid,
      showCrosshair: true,
      showTimeScale: true,
      showPriceScale: true
    })
  }

  // 加载初始数据
  await loadChartData()
})

onUnmounted(() => {
  // 清理资源
  if (dataSubscription.value) {
    dataSubscription.value()
  }

  if (chartService.value) {
    chartService.value.destroy()
  }
})
</script>

<style scoped lang="less">
.trading-view-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #1e1e1e;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
  height: 48px;

  .toolbar-left, .toolbar-center, .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .exchange-selector {
    background: #3a3a3a;
    color: #ffffff;
    border: 1px solid #4a4a4a;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: #0078d4;
    }
  }

  .symbol-search {
    position: relative;

    .symbol-input {
      background: #3a3a3a;
      color: #ffffff;
      border: 1px solid #4a4a4a;
      border-radius: 4px;
      padding: 6px 12px;
      font-size: 14px;
      width: 200px;

      &:focus {
        outline: none;
        border-color: #0078d4;
      }

      &::placeholder {
        color: #888888;
      }
    }

    .symbol-suggestions {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: #2a2a2a;
      border: 1px solid #4a4a4a;
      border-top: none;
      border-radius: 0 0 4px 4px;
      max-height: 200px;
      overflow-y: auto;
      z-index: 1000;

      .symbol-suggestion {
        padding: 8px 12px;
        cursor: pointer;
        font-size: 14px;

        &:hover {
          background: #3a3a3a;
        }
      }
    }
  }

  .current-symbol {
    font-weight: 600;
    font-size: 16px;
    color: #ffffff;
  }

  .timeframe-selector {
    display: flex;
    gap: 4px;

    .timeframe-btn {
      background: transparent;
      color: #cccccc;
      border: 1px solid #4a4a4a;
      border-radius: 4px;
      padding: 6px 12px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: #3a3a3a;
        color: #ffffff;
      }

      &.active {
        background: #0078d4;
        color: #ffffff;
        border-color: #0078d4;
      }
    }
  }

  .indicator-btn, .settings-btn {
    background: #3a3a3a;
    color: #ffffff;
    border: 1px solid #4a4a4a;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: #4a4a4a;
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left-panel {
  width: 300px;
  background: #2a2a2a;
  border-right: 1px solid #3a3a3a;
  display: flex;
  flex-direction: column;

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #3a3a3a;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }

    .close-btn {
      background: transparent;
      color: #cccccc;
      border: none;
      font-size: 18px;
      cursor: pointer;
      padding: 4px;

      &:hover {
        color: #ffffff;
      }
    }
  }

  .indicator-categories {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    .category {
      margin-bottom: 24px;

      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #cccccc;
      }

      .indicator-list {
        .indicator-item {
          padding: 8px 12px;
          background: #3a3a3a;
          border: 1px solid #4a4a4a;
          border-radius: 4px;
          margin-bottom: 4px;
          cursor: pointer;
          font-size: 14px;
          transition: all 0.2s;

          &:hover {
            background: #4a4a4a;
            border-color: #0078d4;
          }
        }
      }

      .add-custom-btn {
        width: 100%;
        padding: 8px 12px;
        background: #0078d4;
        color: #ffffff;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background: #106ebe;
        }
      }
    }
  }
}

.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #1e1e1e;

  &.with-panel {
    margin-left: 0; // 当有左侧面板时的样式调整
  }

  .main-chart {
    flex: 1;
    position: relative;
    min-height: 400px;

    .chart-element {
      width: 100%;
      height: 100%;
    }

    .chart-indicators {
      position: absolute;
      top: 8px;
      left: 8px;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .indicator-control {
        display: flex;
        align-items: center;
        gap: 4px;
        background: rgba(42, 42, 42, 0.9);
        border: 1px solid #4a4a4a;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 12px;

        .config-btn, .remove-btn {
          background: transparent;
          color: #cccccc;
          border: none;
          font-size: 12px;
          cursor: pointer;
          padding: 2px;

          &:hover {
            color: #ffffff;
          }
        }

        .remove-btn:hover {
          color: #ff4444;
        }
      }
    }
  }

  .sub-charts {
    .sub-chart {
      height: 150px;
      border-top: 1px solid #3a3a3a;
      position: relative;

      .sub-chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 4px 8px;
        background: #2a2a2a;
        border-bottom: 1px solid #3a3a3a;
        font-size: 12px;

        .sub-chart-controls {
          display: flex;
          gap: 4px;

          .config-btn, .remove-btn {
            background: transparent;
            color: #cccccc;
            border: none;
            font-size: 12px;
            cursor: pointer;
            padding: 2px;

            &:hover {
              color: #ffffff;
            }
          }

          .remove-btn:hover {
            color: #ff4444;
          }
        }
      }

      .chart-element {
        width: 100%;
        height: calc(100% - 28px);
      }
    }
  }
}

// 模态框样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.custom-editor-modal {
  background: #2a2a2a;
  border: 1px solid #4a4a4a;
  border-radius: 8px;
  width: 80%;
  max-width: 800px;
  height: 80%;
  max-height: 600px;
  display: flex;
  flex-direction: column;

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #3a3a3a;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .close-btn {
      background: transparent;
      color: #cccccc;
      border: none;
      font-size: 20px;
      cursor: pointer;
      padding: 4px;

      &:hover {
        color: #ffffff;
      }
    }
  }

  .editor-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    .editor-toolbar {
      display: flex;
      gap: 8px;
      padding: 12px 20px;
      border-bottom: 1px solid #3a3a3a;

      .save-btn, .test-btn {
        padding: 6px 16px;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;
      }

      .save-btn {
        background: #0078d4;
        color: #ffffff;

        &:hover {
          background: #106ebe;
        }
      }

      .test-btn {
        background: #3a3a3a;
        color: #ffffff;
        border: 1px solid #4a4a4a;

        &:hover {
          background: #4a4a4a;
        }
      }
    }

    .code-editor {
      flex: 1;
      background: #1e1e1e;
      color: #ffffff;
      border: none;
      padding: 20px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 14px;
      line-height: 1.5;
      resize: none;

      &:focus {
        outline: none;
      }

      &::placeholder {
        color: #666666;
      }
    }
  }
}

// 设置面板样式
.settings-panel {
  position: fixed;
  top: 48px;
  right: 0;
  width: 300px;
  height: calc(100vh - 48px);
  background: #2a2a2a;
  border-left: 1px solid #3a3a3a;
  display: flex;
  flex-direction: column;
  z-index: 1000;

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #3a3a3a;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }

    .close-btn {
      background: transparent;
      color: #cccccc;
      border: none;
      font-size: 18px;
      cursor: pointer;
      padding: 4px;

      &:hover {
        color: #ffffff;
      }
    }
  }

  .settings-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    .setting-group {
      margin-bottom: 24px;

      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #cccccc;
      }

      label {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-size: 14px;
        cursor: pointer;

        input[type="checkbox"] {
          width: 16px;
          height: 16px;
          accent-color: #0078d4;
        }

        input[type="number"] {
          background: #3a3a3a;
          color: #ffffff;
          border: 1px solid #4a4a4a;
          border-radius: 4px;
          padding: 4px 8px;
          font-size: 14px;
          width: 80px;

          &:focus {
            outline: none;
            border-color: #0078d4;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .toolbar {
    .toolbar-left, .toolbar-center, .toolbar-right {
      gap: 8px;
    }

    .symbol-search .symbol-input {
      width: 150px;
    }

    .timeframe-selector {
      .timeframe-btn {
        padding: 4px 8px;
        font-size: 11px;
      }
    }
  }

  .left-panel {
    width: 250px;
  }

  .custom-editor-modal {
    width: 95%;
    height: 90%;
  }

  .settings-panel {
    width: 250px;
  }
}

@media (max-width: 480px) {
  .toolbar {
    flex-wrap: wrap;
    height: auto;
    padding: 8px;

    .toolbar-left, .toolbar-center, .toolbar-right {
      flex: 1;
      min-width: 0;
    }

    .toolbar-center {
      order: 3;
      flex-basis: 100%;
      margin-top: 8px;
    }
  }

  .left-panel {
    width: 100%;
    position: fixed;
    top: 48px;
    left: 0;
    height: calc(100vh - 48px);
    z-index: 1500;
  }

  .chart-container.with-panel {
    display: none;
  }
}
</style>