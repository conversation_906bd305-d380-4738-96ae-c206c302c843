/*
 * @Author: zgr126 <EMAIL>
 * @Date: 2025-07-09
 * @Description: 图表服务 - 基于lightweight-charts的增强图表管理
 * 
 * Copyright (c) 2025 by zgr126, All Rights Reserved. 
 */

import { 
  createChart, 
  IChartApi, 
  ICandlestickSeries, 
  IHistogramSeries,
  ILineSeries,
  CandlestickData,
  HistogramData,
  LineData,
  DeepPartial,
  ChartOptions,
  SeriesOptionsCommon
} from 'lightweight-charts'
import { OHLCV } from 'ccxt'

export interface ChartConfig {
  container: HTMLElement
  width?: number
  height?: number
  theme?: 'light' | 'dark'
  showVolume?: boolean
  showGrid?: boolean
  showCrosshair?: boolean
  showTimeScale?: boolean
  showPriceScale?: boolean
}

export interface SeriesConfig {
  id: string
  type: 'candlestick' | 'line' | 'histogram' | 'area'
  title: string
  overlay?: boolean
  color?: string
  lineWidth?: number
  priceScaleId?: string
}

export interface IndicatorSeries {
  id: string
  series: ILineSeries | IHistogramSeries
  config: SeriesConfig
}

export class ChartService {
  private chart: IChartApi | null = null
  private candlestickSeries: ICandlestickSeries | null = null
  private volumeSeries: IHistogramSeries | null = null
  private indicatorSeries: Map<string, IndicatorSeries> = new Map()
  private container: HTMLElement | null = null
  private config: ChartConfig
  private resizeObserver: ResizeObserver | null = null

  constructor(config: ChartConfig) {
    this.config = config
    this.container = config.container
    this.initializeChart()
  }

  /**
   * 初始化图表
   */
  private initializeChart() {
    if (!this.container) return

    const chartOptions: DeepPartial<ChartOptions> = {
      width: this.config.width || this.container.clientWidth,
      height: this.config.height || this.container.clientHeight,
      layout: {
        background: {
          type: 'solid',
          color: this.config.theme === 'light' ? '#ffffff' : '#1e1e1e'
        },
        textColor: this.config.theme === 'light' ? '#333333' : '#ffffff',
        fontSize: 12,
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      },
      grid: {
        vertLines: {
          color: this.config.theme === 'light' ? '#f0f0f0' : '#2a2a2a',
          visible: this.config.showGrid !== false
        },
        horzLines: {
          color: this.config.theme === 'light' ? '#f0f0f0' : '#2a2a2a',
          visible: this.config.showGrid !== false
        }
      },
      crosshair: {
        mode: this.config.showCrosshair !== false ? 1 : 0,
        vertLine: {
          color: this.config.theme === 'light' ? '#758696' : '#758696',
          width: 1,
          style: 3
        },
        horzLine: {
          color: this.config.theme === 'light' ? '#758696' : '#758696',
          width: 1,
          style: 3
        }
      },
      timeScale: {
        visible: this.config.showTimeScale !== false,
        timeVisible: true,
        secondsVisible: false,
        borderColor: this.config.theme === 'light' ? '#e1e1e1' : '#3a3a3a',
        fixLeftEdge: true,
        fixRightEdge: true
      },
      rightPriceScale: {
        visible: this.config.showPriceScale !== false,
        borderColor: this.config.theme === 'light' ? '#e1e1e1' : '#3a3a3a',
        scaleMargins: {
          top: 0.1,
          bottom: 0.1
        }
      },
      leftPriceScale: {
        visible: false
      },
      handleScroll: {
        mouseWheel: true,
        pressedMouseMove: true,
        horzTouchDrag: true,
        vertTouchDrag: true
      },
      handleScale: {
        axisPressedMouseMove: true,
        mouseWheel: true,
        pinch: true
      }
    }

    this.chart = createChart(this.container, chartOptions)

    // 创建主K线图
    this.candlestickSeries = this.chart.addCandlestickSeries({
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350'
    })

    // 创建成交量图（如果启用）
    if (this.config.showVolume !== false) {
      this.volumeSeries = this.chart.addHistogramSeries({
        color: '#26a69a',
        priceFormat: {
          type: 'volume'
        },
        priceScaleId: 'volume',
        scaleMargins: {
          top: 0.8,
          bottom: 0
        }
      })
    }

    // 设置自动调整大小
    this.setupAutoResize()
  }

  /**
   * 设置自动调整大小
   */
  private setupAutoResize() {
    if (!this.container || !this.chart) return

    this.resizeObserver = new ResizeObserver(entries => {
      if (entries.length === 0 || !this.chart) return
      
      const { width, height } = entries[0].contentRect
      this.chart.applyOptions({ width, height })
    })

    this.resizeObserver.observe(this.container)
  }

  /**
   * 更新OHLCV数据
   */
  updateOHLCV(ohlcv: OHLCV[]) {
    if (!this.candlestickSeries) return

    const candlestickData: CandlestickData[] = ohlcv.map(([timestamp, open, high, low, close]) => ({
      time: Math.floor(timestamp / 1000) as any,
      open,
      high,
      low,
      close
    }))

    this.candlestickSeries.setData(candlestickData)

    // 更新成交量数据
    if (this.volumeSeries) {
      const volumeData: HistogramData[] = ohlcv.map(([timestamp, , , , close, volume]) => ({
        time: Math.floor(timestamp / 1000) as any,
        value: volume,
        color: close >= ohlcv[0][1] ? '#26a69a80' : '#ef535080' // 半透明
      }))

      this.volumeSeries.setData(volumeData)
    }

    // 自动调整视图
    this.chart?.timeScale().fitContent()
  }

  /**
   * 添加指标系列
   */
  addIndicatorSeries(config: SeriesConfig): string {
    if (!this.chart) return ''

    let series: ILineSeries | IHistogramSeries

    const commonOptions: DeepPartial<SeriesOptionsCommon> = {
      title: config.title,
      priceScaleId: config.priceScaleId || (config.overlay ? 'right' : config.id)
    }

    switch (config.type) {
      case 'line':
        series = this.chart.addLineSeries({
          ...commonOptions,
          color: config.color || '#2196f3',
          lineWidth: config.lineWidth || 2,
          crosshairMarkerVisible: true,
          crosshairMarkerRadius: 4
        })
        break

      case 'histogram':
        series = this.chart.addHistogramSeries({
          ...commonOptions,
          color: config.color || '#2196f3'
        })
        break

      case 'area':
        series = this.chart.addAreaSeries({
          ...commonOptions,
          topColor: config.color || '#2196f380',
          bottomColor: config.color || '#2196f300',
          lineColor: config.color || '#2196f3',
          lineWidth: config.lineWidth || 2
        })
        break

      default:
        throw new Error(`不支持的系列类型: ${config.type}`)
    }

    const indicatorSeries: IndicatorSeries = {
      id: config.id,
      series,
      config
    }

    this.indicatorSeries.set(config.id, indicatorSeries)

    // 如果不是覆盖图，创建独立的价格刻度
    if (!config.overlay) {
      this.chart.priceScale(config.priceScaleId || config.id).applyOptions({
        scaleMargins: {
          top: 0.1,
          bottom: 0.1
        }
      })
    }

    return config.id
  }

  /**
   * 更新指标数据
   */
  updateIndicatorData(seriesId: string, data: LineData[] | HistogramData[]) {
    const indicatorSeries = this.indicatorSeries.get(seriesId)
    if (!indicatorSeries) return

    indicatorSeries.series.setData(data as any)
  }

  /**
   * 移除指标系列
   */
  removeIndicatorSeries(seriesId: string) {
    const indicatorSeries = this.indicatorSeries.get(seriesId)
    if (!indicatorSeries || !this.chart) return

    this.chart.removeSeries(indicatorSeries.series)
    this.indicatorSeries.delete(seriesId)
  }

  /**
   * 获取指标系列
   */
  getIndicatorSeries(seriesId: string): IndicatorSeries | undefined {
    return this.indicatorSeries.get(seriesId)
  }

  /**
   * 获取所有指标系列
   */
  getAllIndicatorSeries(): IndicatorSeries[] {
    return Array.from(this.indicatorSeries.values())
  }

  /**
   * 更新图表主题
   */
  updateTheme(theme: 'light' | 'dark') {
    if (!this.chart) return

    this.config.theme = theme

    const backgroundColor = theme === 'light' ? '#ffffff' : '#1e1e1e'
    const textColor = theme === 'light' ? '#333333' : '#ffffff'
    const gridColor = theme === 'light' ? '#f0f0f0' : '#2a2a2a'
    const borderColor = theme === 'light' ? '#e1e1e1' : '#3a3a3a'

    this.chart.applyOptions({
      layout: {
        background: { type: 'solid', color: backgroundColor },
        textColor
      },
      grid: {
        vertLines: { color: gridColor },
        horzLines: { color: gridColor }
      },
      timeScale: {
        borderColor
      },
      rightPriceScale: {
        borderColor
      }
    })
  }

  /**
   * 更新图表选项
   */
  updateOptions(options: Partial<ChartConfig>) {
    if (!this.chart) return

    Object.assign(this.config, options)

    if (options.showGrid !== undefined) {
      this.chart.applyOptions({
        grid: {
          vertLines: { visible: options.showGrid },
          horzLines: { visible: options.showGrid }
        }
      })
    }

    if (options.showCrosshair !== undefined) {
      this.chart.applyOptions({
        crosshair: {
          mode: options.showCrosshair ? 1 : 0
        }
      })
    }

    if (options.showTimeScale !== undefined) {
      this.chart.applyOptions({
        timeScale: {
          visible: options.showTimeScale
        }
      })
    }

    if (options.showPriceScale !== undefined) {
      this.chart.applyOptions({
        rightPriceScale: {
          visible: options.showPriceScale
        }
      })
    }
  }

  /**
   * 获取图表实例
   */
  getChart(): IChartApi | null {
    return this.chart
  }

  /**
   * 获取K线系列
   */
  getCandlestickSeries(): ICandlestickSeries | null {
    return this.candlestickSeries
  }

  /**
   * 获取成交量系列
   */
  getVolumeSeries(): IHistogramSeries | null {
    return this.volumeSeries
  }

  /**
   * 截图
   */
  takeScreenshot(): string | null {
    if (!this.chart) return null
    return this.chart.takeScreenshot().toDataURL()
  }

  /**
   * 销毁图表
   */
  destroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
      this.resizeObserver = null
    }

    if (this.chart) {
      this.chart.remove()
      this.chart = null
    }

    this.candlestickSeries = null
    this.volumeSeries = null
    this.indicatorSeries.clear()
    this.container = null
  }
}
